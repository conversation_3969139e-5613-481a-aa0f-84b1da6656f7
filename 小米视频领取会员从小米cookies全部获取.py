import requests
import uuid
import time
import random
import json
import os

# 配置选项
ENABLE_MUSIC_TASK = False  # 是否启用音乐任务，True为启用，False为禁用

# 定义公共 headers 函数
def get_common_headers(cookie):
    return {
        'Host': 'm.jr.airstarfinance.net',
        'Connection': 'keep-alive',
        'X-Request-ID': str(uuid.uuid4()),
        'sec-ch-ua-platform': '"Android"',
        'Cache-Control': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Linux; U; Android 13; zh-CN; M2012K10C Build/TP1A.220624.014) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'Accept': 'application/json, text/plain, */*',
        'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Android WebView";v="132"',
        'sec-ch-ua-mobile': '?1',
        'X-Requested-With': 'com.mipay.wallet',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Cookie': cookie
    }

# 新增函数：从文件读取Cookie列表
def read_cookies(file_path):
    """从文件读取Cookie列表，兼容不同格式"""
    cookie_parts_list = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            parts = line.split('----')
            if len(parts) < 2:  # 至少需要手机号和cookie两部分
                continue
                
            phone = parts[0]
            cookie = parts[1]
            
            # 处理params和jrairstar_ph
            if len(parts) >= 4:
                # 提取musicVersion和session_id
                params = parts[2]
                if 'musicVersion' in params:
                    params_dict = dict(item.split('=') for item in params.split('&'))
                    params = f"musicVersion={params_dict.get('musicVersion', '')}"
                
                # 获取jrairstar_ph，不管后面是否还有其他部分
                jrairstar_ph = parts[3].split('----')[0].strip()  # 只取第一部分，忽略后面的额外验证信息
            else:
                # 如果格式不完整，使用默认值
                params = "musicVersion=********"
                jrairstar_ph = "default_ph"
            
            cookie_parts_list.append((phone, cookie, params, jrairstar_ph))
    
    return cookie_parts_list

def get_task_id(cookie):
    """第一步：获取任务ID"""
    headers = get_common_headers(cookie)
    params = {
        'activityCode': '2211-videoWelfare',
        'app': 'com.mipay.wallet',
        'oaid': '9d593fabcf70d61e',
        'regId': 'IdQl%2Fa%2F5XdUdUyTUv2%2FeaT%2B7cY3gv3dkCQbEkruAqBSiPBSUEhFRiZHLNjOK7uUA',
        'versionCode': '20577595',
        'versionName': '6.89.1.5275.2323',
        'isNfcPhone': 'true',
        'channel': 'mipay_indexcard_TVcard',
        'deviceType': '2',
        'system': '1',
        'visitEnvironment': '2',
        'userExtra': '{"platformType":1,"com.miui.player":"*******","com.miui.video":"v2022101590(MiVideo-ROM)","com.mipay.wallet":"6.89.1.5275.2323"}',
        'taskId': '813',
        'browsTaskId': '7',
        'browsClickUrlId': '1158960009',
        'clickEntryType': 'undefined',
        'adInfoId': '130720141',
        'triggerId': str(uuid.uuid4()),
        'festivalStatus': '0'
    }
    
    url = 'https://m.jr.airstarfinance.net/mp/api/generalActivity/completeTask'
    try:
        # 明确禁用代理
        session = requests.Session()
        session.trust_env = False  # 禁用从环境变量读取代理设置
        response = session.get(url, headers=headers, params=params, timeout=10)
        return response.json()
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return {"code": -1, "error": str(e)}

def complete_task(cookie, user_task_id):
    """第二步：完成任务抽奖"""
    headers = get_common_headers(cookie)
    params = {
        'imei': '',
        'device': 'ares',
        'appLimit': '{"com.qiyi.video":false,"com.youku.phone":false,"com.tencent.qqlive":false,"com.hunantv.imgo.activity":false,"com.cmcc.cmvideo":false,"com.sankuai.meituan":false,"com.anjuke.android.app":false,"com.tal.abctimelibrary":false,"com.lianjia.beike":false,"com.kmxs.reader":false,"com.jd.jrapp":false,"com.smile.gifmaker":false,"com.kuaishou.nebula":true}',
        'activityCode': '2211-videoWelfare',
        'userTaskId': str(user_task_id),
        'app': 'com.mipay.wallet',
        'oaid': '9d593fabcf70d61e',
        'regId': 'IdQl%2Fa%2F5XdUdUyTUv2%2FeaT%2B7cY3gv3dkCQbEkruAqBSiPBSUEhFRiZHLNjOK7uUA',
        'versionCode': '20577595',
        'versionName': '6.89.1.5275.2323',
        'isNfcPhone': 'true',
        'channel': 'mipay_indexcard_TVcard',
        'deviceType': '2',
        'system': '1',
        'visitEnvironment': '2',
        'userExtra': '{"platformType":1,"com.miui.player":"*******","com.miui.video":"v2022101590(MiVideo-ROM)","com.mipay.wallet":"6.89.1.5275.2323"}'
    }
    
    url = 'https://m.jr.airstarfinance.net/mp/api/generalActivity/luckDraw'
    try:
        # 明确禁用代理
        session = requests.Session()
        session.trust_env = False  # 禁用从环境变量读取代理设置
        response = session.get(url, headers=headers, params=params, timeout=10)
        return response.json()
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return {"code": -1, "error": str(e)}

def get_task_list(cookie, jrairstar_ph):
    """获取普通任务列表"""
    headers = {
        'Host': 'm.jr.airstarfinance.net',
        'Connection': 'keep-alive',
        'Content-Length': '1780',
        'Accept': 'application/json, text/plain, */*',
        'Cache-Control': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Linux; U; Android 13; zh-CN; Mi 10 Build/TKQ1.221114.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'X-Request-ID': str(uuid.uuid4()),
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://m.jr.airstarfinance.net',
        'X-Requested-With': 'com.mipay.wallet',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Accept-Encoding': 'gzip, deflate',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Cookie': cookie
    }
    
    data = {
        'isNfcPhone': 'true',
        'deviceType': '2',
        'device': 'umi',
        'appLimit': '{"com.qiyi.video":false,"com.youku.phone":false,"com.tencent.qqlive":false,"com.hunantv.imgo.activity":false,"com.cmcc.cmvideo":false,"com.sankuai.meituan":false,"com.anjuke.android.app":false,"com.tal.abctimelibrary":false,"com.lianjia.beike":false,"com.kmxs.reader":false,"com.jd.jrapp":false,"com.smile.gifmaker":false,"com.kuaishou.nebula":false}',
        'pagination': '0',
        'dataType': '0',
        'activityCode': '2211-videoWelfare',
        'app': 'com.mipay.wallet',
        'oaid': 'c63f3e020bc528c4',
        'regId': 'DKfgp%2FFWzHV91JgTdZuRbi65IKiF9OLxS6O9oFnKUWxHRJJgMWl%2F5BlEzpzGMdW3',
        'versionCode': '20577595',
        'versionName': '6.89.1.5275.2323',
        'channel': 'mipay_FLbanner_TVcard',
        'system': '1',
        'visitEnvironment': '2',
        'userExtra': '{"platformType":1,"com.miui.player":"*********","com.miui.video":"v2022101590(MiVideo-ROM)","com.mipay.wallet":"6.89.1.5275.2323"}',
        'yimiData': '{"clientInfo":{"deviceInfo":{"androidVersion":"33","device":"umi","miuiVersion":14,"miuiVersionName":"V140","model":"Mi 10","restrictImei":"true","screenHeight":851,"screenWidth":393},"userInfo":{"androidId":"51f743b0f42a506d","connectionType":"WIFI","oaid":"c63f3e020bc528c4","country":"CN","isPersonalizedAdEnabled":true,"language":"zh-rCN"},"appInfo":{"packageName":"com.mipay.wallet","version":"6.89.1.5275.2323"},"context":{},"impRequests":[{"adsCount":1,"tagId":"*********"}]}}',
        'jrairstar_ph': jrairstar_ph
    }
    
    url = 'https://m.jr.airstarfinance.net/mp/api/generalActivity/getTaskList'
    try:
        session = requests.Session()
        session.trust_env = False
        response = session.post(url, headers=headers, data=data, timeout=10)
        
        # 新增响应内容展示
        print(f"🔍 服务器返回状态码: {response.status_code}")
        print("🔍 完整响应内容:")

        
        return response.json()
    except Exception as e:
        print(f"❌ 获取任务列表异常: {str(e)}")
        return {"code": -1, "error": str(e)}

def complete_all_tasks(cookie, jrairstar_ph, phone):
    """完成所有可完成的任务（普通任务流程）"""
    # 获取任务列表
    task_list_result = get_task_list(cookie, jrairstar_ph)
    if task_list_result.get('code') != 0:
        print(f"❌ 获取任务列表失败: {task_list_result.get('error')}")
        return
    
    task_list = task_list_result.get('value', {}).get('taskInfoList', [])
    if not task_list:
        print("⚠️ 未找到任何任务")
        return
    
    print(f"📋 共找到 {len(task_list)} 个任务")
    
    for task in task_list:
        task_id = task.get('taskId')
        task_name = task.get('taskName')
        complete_status = task.get('completeStatus')
        user_task_id = task.get('userTaskId')
        task_code = task.get('taskCode')
        
        print(f"\n🔍 任务ID: {task_id}, 名称: {task_name}, 状态: {complete_status}")
        
        # 特殊处理FINANCE_FIRSTIN任务
        if task_code == 'FINANCE_FIRSTIN' and not user_task_id:
            print("🔍 检测到FINANCE_FIRSTIN任务，尝试访问首页获取user_task_id")
            visit_result = visit_index(cookie, jrairstar_ph)
            if visit_result.get('code') == 0:
                print("✅ 成功访问首页，重新获取任务列表")
                return complete_all_tasks(cookie, jrairstar_ph, phone)
            else:
                print(f"❌ 访问首页失败: {visit_result.get('error')}")
                continue
        
        # 尝试完成所有有user_task_id的任务，无论状态如何
        if user_task_id:  # 只要有user_task_id就尝试执行
            print(f"🎯 强制尝试完成任务: {task_name} (ID: {user_task_id})")
            draw_result = complete_task(cookie, user_task_id)
            
            if draw_result.get('code') == 0:
                prize_info = draw_result.get('value', {}).get('prizeInfo', {})
                prize_name = prize_info.get('prizeName', '')
                print(f"🎉 成功领取奖励: {prize_name}")
                # 只要成功领取奖励就保存为白号
                save_white_account(phone)
            else:
                error_msg = draw_result.get('error', '')
                print(f"❌ 领取奖励失败: {error_msg}")
        else:
            print("⏩ 跳过任务 (无user_task_id)")

def finish_music_task(cookie_parts):
    """完成音乐任务前置请求"""
    phone, cookie, params, jrairstar_ph = cookie_parts
    headers = {
        'Host': 'm.jr.airstarfinance.net',
        'Connection': 'keep-alive',
        'X-Request-ID': str(uuid.uuid4()),
        'sec-ch-ua-platform': '"Android"',
        'Cache-Control': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Linux; U; Android 13; zh-CN; M2012K10C Build/TP1A.220624.014) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': cookie
    }
    
    # 使用固定的session_id
    session_id = '70ed5478-fd42-45cf-95f9-ea029d99ca4e1744729304705'
    
    data = {
        'musicVersion': '********',  # 固定值
        'session_id': session_id,
        'jrairstar_ph': jrairstar_ph  # 直接使用传入的未编码值
    }
    
    # 打印请求参数用于调试
    print(f"🔍 请求参数(未编码): {data}")
    
    url = 'https://m.jr.airstarfinance.net/mp/api/qqMusicActivity/finishMusicTask'
    try:
        session = requests.Session()
        session.trust_env = False
        response = session.post(url, headers=headers, data=data, timeout=10)
        
        print(f"🔍 响应状态码: {response.status_code}")
        print(f"🔍 响应内容: {response.text[:500]}")
        
        return response.json()
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return {"code": -1, "error": str(e)}

def get_music_task_id(cookie):
    """获取音乐任务ID"""
    headers = {
        'Host': 'm.jr.airstarfinance.net',
        'Connection': 'keep-alive',
        'User-Agent': 'Mozilla/5.0 (Linux; U; Android 13; zh-CN; Mi 10 Build/TKQ1.221114.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
        'X-Request-ID': str(uuid.uuid4()),
        'X-Requested-With': 'com.mipay.wallet',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Cookie': cookie
    }
    
    params = {
        'activityCode': 'qq-music-201303',
        'taskId': '789',
        'app': 'com.mipay.wallet',
        'oaid': 'c63f3e020bc528c4',
        'regId': 'DKfgp/FWzHV91JgTdZuRbi65IKiF9OLxS6O9oFnKUWxHRJJgMWl/5BlEzpzGMdW3',
        'versionCode': '20577595',
        'versionName': '6.89.1.5275.2323',
        'isNfcPhone': 'true',
        'channel': 'mipay_FLbanner_mymusic',
        'deviceType': '2',
        'system': '1',
        'visitEnvironment': '2',
        'userExtra': '{"platformType":1,"com.miui.player":"*********","com.miui.video":"v2022101590(MiVideo-ROM)","com.mipay.wallet":"6.89.1.5275.2323"}'
    }
    
    url = 'https://m.jr.airstarfinance.net/mp/api/generalActivity/completeTask'
    try:
        session = requests.Session()
        session.trust_env = False
        response = session.get(url, headers=headers, params=params, timeout=10)
        
        # 新增调试信息
        (f"🔍 请求URL: {response.url}")
        (f"🔍 响应内容: {response.text}")
        
        return response.json()
    except Exception as e:
        print(f"❌ 获取音乐任务ID异常: {str(e)}")
        return {"code": -1, "error": str(e)}

def complete_music_task(cookie, user_task_id):
    """完成音乐任务抽奖"""
    headers = {
        'Host': 'm.jr.airstarfinance.net',
        'Connection': 'keep-alive',
        'User-Agent': 'Mozilla/5.0 (Linux; U; Android 13; zh-CN; Mi 10 Build/TKQ1.221114.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
        'X-Request-ID': str(uuid.uuid4()),
        'X-Requested-With': 'com.mipay.wallet',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Cookie': cookie
    }
    
    params = {
        'activityCode': 'qq-music-201303',
        'userTaskId': str(user_task_id),
        'app': 'com.mipay.wallet',
        'oaid': 'c63f3e020bc528c4',
        'regId': 'DKfgp/FWzHV91JgTdZuRbi65IKiF9OLxS6O9oFnKUWxHRJJgMWl/5BlEzpzGMdW3',
        'versionCode': '20577595',
        'versionName': '6.89.1.5275.2323',
        'isNfcPhone': 'true',
        'channel': 'mipay_FLbanner_mymusic',
        'deviceType': '2',
        'system': '1',
        'visitEnvironment': '2',
        'userExtra': '{"platformType":1,"com.miui.player":"*********","com.miui.video":"v2022101590(MiVideo-ROM)","com.mipay.wallet":"6.89.1.5275.2323"}'
    }
    
    url = 'https://m.jr.airstarfinance.net/mp/api/generalActivity/luckDraw'
    try:
        session = requests.Session()
        session.trust_env = False
        response = session.get(url, headers=headers, params=params, timeout=10)
        
        # 新增调试信息
        (f"🔍 请求URL: {response.url}")
        (f"🔍 响应内容: {response.text}")
        
        return response.json()
    except Exception as e:
        print(f"❌ 音乐任务抽奖异常: {str(e)}")
        return {"code": -1, "error": str(e)}

def run_music_tasks(cookie_parts):
    """执行音乐任务（两种方式完全独立）"""
    import time
    phone, cookie, params, jrairstar_ph = cookie_parts  # 现在解包4个元素
    
    # 方式1：音乐任务专用流程
    while True:
        # 1. 完成音乐任务前置请求
        finish_result = finish_music_task((phone, cookie, params, jrairstar_ph))  # 修改这里，传入4个参数
        if finish_result.get('code') != 0:
            print("❌ 音乐任务前置请求失败，可能已达到上限")
            break
        
        # 2. 获取音乐任务ID
        task_result = get_music_task_id(cookie)
        if task_result.get('code') != 0:
            print("❌ 获取音乐任务ID失败，可能已达到上限")
            break
        
        user_task_id = task_result.get('value')
        print(f"✅ 成功获取音乐任务ID: {user_task_id}")
        
        # 3. 完成音乐任务抽奖
        draw_result = complete_music_task(cookie, user_task_id)
        if draw_result.get('code') == 0:
            prize_info = draw_result.get('value', {}).get('prizeInfo', {})
            print(f"🎵 成功领取音乐奖励: {prize_info.get('prizeName')}")
        else:
            error_msg = draw_result.get('error', '')
            if "您的账号存在安全风险" in error_msg:
                print("⚠️ 账号存在安全风险，跳过该账号")
                break  # 直接跳出循环，跳过该账号
            print(f"❌ 音乐任务抽奖失败: {error_msg}")
        
        print("⏳ 等待10秒后继续...")
        time.sleep(10)

def visit_index(cookie, jrairstar_ph):
    """访问首页获取user_task_id"""
    headers = get_common_headers(cookie)
    headers['Content-Type'] = 'application/x-www-form-urlencoded'
    
    data = {
        'activityCode': '2211-videoWelfare',
        'app': 'com.mipay.wallet',
        'oaid': '8d0545f7df50ffce',
        'regId': 'stQDQK%2FuPxpqhRJR2zGNKWzwTdMOAaA3DuH0y4EnfZhoRngx3f6Owbi4MV19%2FtR2',
        'versionCode': '20577603',
        'versionName': '6.91.1.5302.2333',
        'isNfcPhone': 'true',
        'channel': 'mipay_FLbanner_TVcard',
        'deviceType': '2',
        'system': '1',
        'visitEnvironment': '2',
        'userExtra': '{\"platformType\":1,\"com.miui.player\":\"********-R\",\"com.miui.video\":\"v2024102690(MiVideo-ROM)\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}',
        'jrairstar_ph': jrairstar_ph
    }
    
    url = 'https://m.jr.airstarfinance.net/mp/api/generalActivity/visitIndex'
    try:
        session = requests.Session()
        session.trust_env = False
        response = session.post(url, headers=headers, data=data, timeout=10)
        return response.json()
    except Exception as e:
        print(f"❌ 访问首页异常: {str(e)}")
        return {"code": -1, "error": str(e)}

def save_white_account(phone):
    """保存白号到文件，避免重复"""
    try:
        # 获取当前工作目录
        current_dir = os.getcwd()
        print(f"🔍 当前工作目录: {current_dir}")
        
        # 构建白号文件的完整路径
        white_file = os.path.join(current_dir, '白号.txt')
        print(f"🔍 白号文件路径: {white_file}")
        
        # 先读取现有白号
        existing_phones = set()
        if os.path.exists(white_file):
            print(f"📂 白号文件已存在")
            with open(white_file, 'r', encoding='utf-8') as f:
                existing_phones = set(line.strip() for line in f)
                print(f"📊 现有白号数量: {len(existing_phones)}")
        else:
            print(f"📂 白号文件不存在，将创建新文件")
        
        # 如果手机号已存在，则不重复添加
        if phone in existing_phones:
            print(f"ℹ️ 白号 {phone} 已存在，跳过保存")
            return
            
        # 追加新白号
        with open(white_file, 'a', encoding='utf-8') as f:
            f.write(f"{phone}\n")
        print(f"✅ 已将白号 {phone} 保存到白号.txt")
    except Exception as e:
        print(f"❌ 保存白号失败: {str(e)}")
        print(f"❌ 错误类型: {type(e).__name__}")
        import traceback
        print(f"❌ 错误堆栈: {traceback.format_exc()}")

def main():
    # 从文件读取Cookie列表
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))  # 获取脚本所在目录
    cookie_file = os.path.join(current_dir, "小米cookie全部.txt")  # 构建本地路径
    
    cookie_parts_list = read_cookies(cookie_file)
    
    if not cookie_parts_list:
        print("❌ 未找到有效的Cookie，请检查文件")
        return
    
    for idx, cookie_parts in enumerate(cookie_parts_list, 1):
        print(f"\n======= 正在处理第 {idx} 个Cookie =======")
        phone = cookie_parts[0]  # 获取手机号
        
        # 1. 执行音乐任务（根据配置决定是否执行）
        if ENABLE_MUSIC_TASK:
            print("\n🎵 开始执行音乐任务")
            run_music_tasks(cookie_parts)
        else:
            print("\n⏭️ 音乐任务已禁用，跳过执行")
        
        # 2. 获取任务列表并完成所有任务
        complete_all_tasks(cookie_parts[1], cookie_parts[3], phone)
        
        # 3. 获取任务ID并完成抽奖（保留原有逻辑）
        task_result = get_task_id(cookie_parts[1])
        if task_result.get('code') == 0 and task_result.get('success'):
            user_task_id = task_result.get('value')
            print(f"✅ 成功获取任务ID: {user_task_id}")
            
            draw_result = complete_task(cookie_parts[1], user_task_id)
            
            if draw_result.get('code') == 0 and draw_result.get('success'):
                prize_info = draw_result.get('value', {}).get('prizeInfo', {})
                prize_name = prize_info.get('prizeName', '')
                print(f"🎉 成功领取奖励: {prize_name}")
                print(f"奖励描述: {prize_info.get('prizeDesc')}")
                print(f"奖励金额: {prize_info.get('amount')}")
                # 只要成功领取奖励就保存为白号
                save_white_account(phone)
            else:
                error_msg = draw_result.get('error', '')
                # 只有当不是安全风险错误，且不是其他常见错误时，才保存为白号
                if "您的账号存在安全风险" not in error_msg and "完成任务失败" not in error_msg:
                    save_white_account(phone)
                print(f"❌ 抽奖失败: {draw_result.get('error')}")
        else:
            error_msg = str(task_result.get('error', ''))
            print(f"❌ 获取任务ID失败: {error_msg}")
            # 只有当不是安全风险错误，且不是其他常见错误时，才保存为白号
            if "您的账号存在安全风险" not in error_msg and "完成任务失败" not in error_msg:
                save_white_account(phone)


if __name__ == '__main__':
    main()