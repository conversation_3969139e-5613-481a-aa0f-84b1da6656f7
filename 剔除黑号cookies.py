import re

def extract_phone_numbers(file_path):
    """从文件中提取手机号"""
    phone_numbers = set()
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                # 使用正则表达式匹配11位手机号
                phones = re.findall(r'1[3-9]\d{9}', line)
                phone_numbers.update(phones)
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
    return phone_numbers

def filter_cookies(phone_numbers, input_file, output_file, excluded_file):
    """根据手机号筛选cookie"""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        matched_lines = []
        excluded_lines = []
        
        for line in lines:
            # 检查行中是否包含任何匹配的手机号
            if any(phone in line for phone in phone_numbers):
                matched_lines.append(line)
            else:
                excluded_lines.append(line)
        
        # 保存匹配的记录
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(matched_lines)
        
        # 追加被剔除的记录到文件末尾
        with open(excluded_file, 'a', encoding='utf-8') as f:
            f.writelines(excluded_lines)
            
        print(f"处理完成！")
        print(f"匹配的记录已保存到: {output_file}")
        print(f"剔除的记录已追加到: {excluded_file}")
        
    except Exception as e:
        print(f"处理文件时出错: {str(e)}")

def process_white_accounts():
    # 读取白号.txt中的手机号
    with open('白号.txt', 'r', encoding='utf-8') as f:
        white_accounts = [line.strip() for line in f if line.strip()]
    
    # 读取白号账号密码.txt中的手机号
    with open('白号账号密码.txt', 'r', encoding='utf-8') as f:
        white_accounts_with_pwd = [line.split('----')[0].strip() for line in f if line.strip()]
    
    # 剔除在白号账号密码.txt中存在的手机号
    filtered_accounts = [account for account in white_accounts if account not in white_accounts_with_pwd]
    
    # 直接更新白号.txt文件
    with open('白号.txt', 'w', encoding='utf-8') as f:
        for account in filtered_accounts:
            f.write(f"{account}\n")
    
    print(f"原始白号数量: {len(white_accounts)}")
    print(f"剔除后白号数量: {len(filtered_accounts)}")
    print(f"已更新白号.txt文件")
    
    # 执行原有的筛选功能
    main()

def main():
    # 文件路径
    white_list_file = "白号.txt"
    input_file = "小米cookie全部.txt"
    output_file = "小米cookie全部.txt"  # 覆盖原文件
    excluded_file = "剔除.txt"
    
    # 提取白号中的手机号
    phone_numbers = extract_phone_numbers(white_list_file)
    print(f"从白号文件中提取到 {len(phone_numbers)} 个手机号")
    
    # 筛选cookie
    filter_cookies(phone_numbers, input_file, output_file, excluded_file)

if __name__ == "__main__":
    process_white_accounts() 