import requests
import os
import json
import time
import uuid
import hashlib
import base64
from urllib.parse import quote
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def md5_encrypt(text):
    """MD5加密"""
    return hashlib.md5(text.encode('utf-8')).hexdigest().upper()

def handle_verification(session, notification_url):
    """处理验证流程"""
    try:
        # 第一步：访问authStart获取identity_session
        auth_start_response = session.get(
            notification_url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Linux; Android 9; MIX 2S Build/PKQ1.180729.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/72.0.3626.121 Mobile Safari/537.36 PassportSDK/PassportHybridView/accountsdk-18.11.26 XiaoMi/HybridView/',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'X-Requested-With': 'com.xiaomi.account'
            },
            allow_redirects=True
        )
        
        print("\n🔍 验证第一步响应:")
        print(f"状态码: {auth_start_response.status_code}")
        print(f"Headers: {dict(auth_start_response.headers)}")
        print(f"Cookies: {auth_start_response.cookies.get_dict()}")
        
        # 获取重定向后的URL中的context参数
        final_url = auth_start_response.url
        context = None
        if 'context=' in final_url:
            context = final_url.split('context=')[1].split('&')[0]
        
        if not context:
            print("❌ 获取context参数失败")
            return None
            
        # 第二步：获取验证问题列表
        list_url = f"https://account.xiaomi.com/identity/list?sid=passportapi&supportedMask=0&context={context}"
        list_response = session.get(
            list_url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Linux; Android 9; MIX 2S Build/PKQ1.180729.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/72.0.3626.121 Mobile Safari/537.36 PassportSDK/PassportHybridView/accountsdk-18.11.26 XiaoMi/HybridView/',
                'Accept': 'application/json, text/plain, */*',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': final_url,
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
            }
        )
        
        print("\n🔍 验证第二步响应:")
        print(f"状态码: {list_response.status_code}")
        print(f"Headers: {dict(list_response.headers)}")
        print(f"Cookies: {list_response.cookies.get_dict()}")
        print(f"响应内容: {list_response.text}")
        
        if list_response.status_code != 200:
            print("❌ 获取验证问题列表失败")
            return None
            
        # 获取identity_session
        identity_session = session.cookies.get('identity_session')
        if not identity_session:
            print("❌ 获取identity_session失败")
            return None
            
        # 第三步：提交密保答案
        answer_url = "https://account.xiaomi.com/identity/auth/answerQuestion"
        answer_data = {
            '_flag': '16',
            'questions': json.dumps([
                {"q": "您外公的姓名", "p": 1, "a": "外公********"},
                {"q": "您小学六年级班主任的名字", "p": 3, "a": "六年级班主任********"},
                {"q": "您外婆的姓名", "p": 0, "a": "外婆********"}
            ]),
            '_json': 'true'
        }
        
        answer_response = session.post(
            answer_url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Linux; Android 9; MIX 2S Build/PKQ1.180729.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/72.0.3626.121 Mobile Safari/537.36 PassportSDK/PassportHybridView/accountsdk-18.11.26 XiaoMi/HybridView/',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Accept': 'application/json, text/plain, */*',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': final_url,
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
            },
            data=answer_data
        )
        
        print("\n🔍 验证第三步响应:")
        print(f"状态码: {answer_response.status_code}")
        print(f"Headers: {dict(answer_response.headers)}")
        print(f"Cookies: {answer_response.cookies.get_dict()}")
        print(f"响应内容: {answer_response.text}")
        
        if answer_response.status_code != 200:
            print("❌ 提交密保答案失败")
            return None
            
        result = parse_login_response(answer_response)
        if result and result.get('code') == 0:
            return result.get('location')
            
        return None
    except Exception as e:
        print(f"❌ 验证流程异常: {str(e)}")
        return None

def get_xiaomi_cookie(username, password):
    """获取小米账号Cookie"""
    # 1. 初始化会话和参数
    session = requests.Session()
    session.verify = False  # 禁用SSL验证
    device_id = str(uuid.uuid4()).replace('-', '')
    base_url = "https://account.xiaomi.com"
    
    # 直接开始构造登录数据
    login_url = f"{base_url}/pass/serviceLoginAuth2"
    login_data = {
        'qs': '%253F_json%253Dtrue%2526sid%253Dpassportapi%2526_locale%253Dzh_CN',
        'callback': 'https://api.account.xiaomi.com/sts?sid=passportapi',
        '_json': 'true',
        '_sign': 'l8bGcCEpX5MKvqcJxU5gDg5DsLE%3D',
        'user': username,
        'hash': md5_encrypt(password),
        'sid': 'passportapi',
        '_locale': 'zh_CN'
    }

    # 4. 发送登录请求
    try:
        print("\n🔍 登录请求参数:")
        print(f"URL: {login_url}")
        print("Headers: {")
        print("    'Content-Type': 'application/x-www-form-urlencoded',")
        print("    'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM',")
        print("    'Accept-Encoding': 'gzip',")
        print("    'Connection': 'Keep-Alive'")
        print("}")
        print(f"Data: {login_data}")
        
        response = session.post(
            login_url,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM',
                'Accept-Encoding': 'gzip',
                'Connection': 'Keep-Alive'
            },
            data=login_data,
            timeout=10
        )
        
        # 显示登录响应的Set-Cookie
        print("\n🔍 登录响应Set-Cookie:")
        print(response.cookies.get_dict())
        
        if response.status_code == 200:
            # 解析登录响应
            result = parse_login_response(response)
            print("\n🔍 登录响应详情:")
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if result:
                # 从响应头中获取passToken
                pass_token = response.cookies.get('passToken')
                if pass_token:
                    result['passToken'] = pass_token
                
                # 从响应头中获取userId
                user_id = response.cookies.get('userId')
                if user_id:
                    result['userId'] = user_id
                
                # 从响应头中获取psecurity
                p_security = response.headers.get('extension-pragma')
                if p_security:
                    try:
                        p_security_json = json.loads(p_security)
                        result['psecurity'] = p_security_json.get('psecurity')
                    except:
                        pass
                
                print("\n🔍 最终获取到的关键信息:")
                print(f"passToken: {result.get('passToken')}")
                print(f"userId: {result.get('userId')}")
                print(f"psecurity: {result.get('psecurity')}")
                
                # 保存这些值到session中
                if result.get('passToken'):
                    session.cookies.set('passToken', result['passToken'], domain='.account.xiaomi.com', path='/')
                if result.get('userId'):
                    session.cookies.set('userId', result['userId'], domain='.account.xiaomi.com', path='/')
                if result.get('psecurity'):
                    session.cookies.set('psecurity', result['psecurity'], domain='.account.xiaomi.com', path='/')
                
                if result.get('code') == 0 and result.get('notificationUrl'):
                    print("⚠️ 需要验证，开始验证流程")
                    location = handle_verification(session, result.get('notificationUrl'))
                    if location:
                        result['location'] = location
                        print("✅ 验证成功")
                    else:
                        print("❌ 验证失败")
                        return None
                elif result.get('code') == 0 and result.get('location'):
                    print("✅ 登录成功")
                else:
                    print(f"❌ 登录失败: {result.get('desc', '未知错误')}")
                    return None
                
                # 5. 获取服务令牌
                sts_url = result.get('location')
                if sts_url:
                    try:
                        print(f"\n🔍 发送STS请求:")
                        print(f"URL: {sts_url}")
                        print(f"Headers: {session.headers}")
                        
                        sts_response = session.get(
                            sts_url,
                            headers={
                                'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
                            },
                            verify=False,
                            timeout=10
                        )
                        
                        print(f"\n🔍 收到STS响应:")
                        print(f"状态码: {sts_response.status_code}")
                        print(f"Headers: {dict(sts_response.headers)}")
                        print(f"Cookies: {sts_response.cookies.get_dict()}")
                        print(f"响应内容: {sts_response.text}")
                        
                        if sts_response.status_code == 200:
                            print("✅ 成功获取服务令牌")
                            service_tokens = [c.value for c in sts_response.cookies if c.name == 'serviceToken']
                            if service_tokens:
                                service_token = service_tokens[0]
                                session.cookies.set('serviceToken', service_token, domain='.xiaomi.com', path='/')
                                
                                # 6. 第四步验证请求 - 米家钱包验证
                                try:
                                    verify_url = "https://account.xiaomi.com/pass/serviceLogin"
                                    verify_params = {
                                        '_json': 'true',
                                        'appName': 'com.mipay.wallet',
                                        'sid': 'jrairstar',
                                        '_locale': 'zh_CN'
                                    }
                                    
                                    verify_response = session.get(
                                        verify_url,
                                        params=verify_params,
                                        headers={
                                            'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
                                        },
                                        verify=False,
                                        timeout=10
                                    )
                                    
                                    if verify_response.status_code == 200:
                                        print("✅ 第四步米家钱包验证成功")
                                        print("\n🔍 第四步请求详情:")
                                        print(f"请求URL: {verify_url}")
                                        print(f"请求参数: {verify_params}")
                                        print(f"请求Headers: {session.headers}")
                                        
                                        verify_result = parse_login_response(verify_response)
                                        print("\n🔍 第四步响应详情:")
                                        print(f"状态码: {verify_response.status_code}")
                                        print(f"响应Headers: {dict(verify_response.headers)}")
                                        print(f"响应内容: {verify_response.text}")
                                        
                                        # 在get_xiaomi_cookie函数中修改第五步的处理
                                        if verify_result and verify_result.get('code') == 0:
                                            print(f"获取到ssecurity: {verify_result.get('ssecurity')}")
                                            
                                            # 执行第五步 - 生成签名URL并获取金融服务令牌
                                            try:
                                                print("\n🔍 开始执行第五步 - 生成签名URL")
                                                # 构造需要签名的JSON响应
                                                response_json = f"&&&START&&&{json.dumps(verify_result)}"
                                                
                                                # 生成签名URL
                                                signed_url = generate_signed_url(response_json)
                                                print(f"生成的签名URL: {signed_url}")
                                                
                                                # 使用签名URL获取金融服务令牌
                                                jr_sts_response = session.get(
                                                    signed_url,
                                                    headers={
                                                        'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
                                                    },
                                                    verify=False,
                                                    timeout=10
                                                )
                                                
                                                print("\n🔍 第五步响应详情:")
                                                print(f"状态码: {jr_sts_response.status_code}")
                                                print(f"Headers: {dict(jr_sts_response.headers)}")
                                                print(f"Cookies: {jr_sts_response.cookies.get_dict()}")
                                                print(f"响应内容: {jr_sts_response.text}")
                                                
                                                if jr_sts_response.status_code == 200:
                                                    print("✅ 成功获取金融服务令牌")
                                                    
                                                    # 获取所有需要的cookie信息
                                                    cookies = session.cookies.get_dict()
                                                    cUserId = cookies.get('cUserId', '')
                                                    serviceToken = cookies.get('serviceToken', '')
                                                    jrairstar_slh = cookies.get('jrairstar_slh', '')
                                                    jrairstar_ph = cookies.get('jrairstar_ph', '')
                                                    
                                                    # 构造cookie字符串
                                                    cookie_str = f"cUserId={cUserId}; jrairstar_serviceToken={serviceToken}; jrairstar_slh={jrairstar_slh}; jrairstar_ph={jrairstar_ph}"
                                                    
                                                    # 构造params参数
                                                    params = "musicVersion=4.32.0.2&session_id=bf94b1dd-20de-4a50-9526-c53aa90dae331744646885463&jrairstar_ph={}".format(
                                                        str(uuid.uuid4()),
                                                        quote(jrairstar_ph)  # 使用已导入的quote函数
                                                    )
                                                    
                                                    # 格式化输出
                                                    formatted_cookie = f"{username}----{cookie_str}----{params}----{jrairstar_ph}"
                                                    print("\n格式化后的Cookie信息:")
                                                    print(formatted_cookie)
                                                    
                                                    # 获取token信息
                                                    pass_token = verify_result.get('passToken')
                                                    user_id = verify_result.get('userId')
                                                    p_security = verify_result.get('psecurity')
                                                    
                                                    print("\n🔍 准备返回的token信息:")
                                                    print(f"passToken: {pass_token}")
                                                    print(f"userId: {user_id}")
                                                    print(f"psecurity: {p_security}")
                                                    
                                                    return {
                                                        'formatted_cookie': formatted_cookie,
                                                        'cookies': cookies,
                                                        'tokens': {
                                                            'passToken': pass_token,
                                                            'userId': user_id,
                                                            'psecurity': p_security,
                                                            'cUserId': cUserId,
                                                            'serviceToken': service_token,
                                                            'ssecurity': verify_result.get('ssecurity'),
                                                            'jr_cookies': jr_sts_response.cookies.get_dict()
                                                        }
                                                    }
                                            except Exception as e:
                                                print(f"❌ 获取金融服务令牌异常: {str(e)}")
                                except Exception as e:
                                    print(f"❌ 第四步验证异常: {str(e)}")
                                
                                # 获取token信息
                                pass_token = verify_result.get('passToken')
                                user_id = verify_result.get('userId')
                                p_security = verify_result.get('psecurity')
                                
                                print("\n🔍 准备返回的token信息:")
                                print(f"passToken: {pass_token}")
                                print(f"userId: {user_id}")
                                print(f"psecurity: {p_security}")
                                
                                return {
                                    'cookies': session.cookies.get_dict(),
                                    'tokens': {
                                        'passToken': pass_token,
                                        'userId': user_id,
                                        'psecurity': p_security,
                                        'cUserId': result.get('cUserId'),
                                        'serviceToken': service_token,
                                        'ssecurity': verify_result.get('ssecurity') if verify_result else None
                                    }
                                }
                    except Exception as e:
                        print(f"❌ 获取服务令牌异常: {str(e)}")
                
                # 获取token信息
                pass_token = result.get('passToken')
                user_id = result.get('userId')
                p_security = result.get('psecurity')
                
                print("\n🔍 准备返回的token信息:")
                print(f"passToken: {pass_token}")
                print(f"userId: {user_id}")
                print(f"psecurity: {p_security}")
                
                return {
                    'cookies': session.cookies.get_dict(),
                    'tokens': {
                        'passToken': pass_token,
                        'userId': user_id,
                        'psecurity': p_security,
                        'cUserId': result.get('cUserId'),
                        'serviceToken': session.cookies.get('serviceToken')
                    }
                }
    except Exception as e:
        print(f"❌ 登录请求异常: {str(e)}")
        return None

def parse_login_response(response):
    """解析登录响应"""
    try:
        text = response.text.replace('&&&START&&&', '')
        print("\n🔍 解析登录响应:")
        print(f"原始响应文本: {response.text}")
        print(f"处理后文本: {text}")
        
        result = json.loads(text)
        print(f"解析后的JSON: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 检查关键字段
        if 'passToken' in result:
            print(f"找到passToken: {result['passToken']}")
        if 'userId' in result:
            print(f"找到userId: {result['userId']}")
        if 'psecurity' in result:
            print(f"找到psecurity: {result['psecurity']}")
            
        return result
    except Exception as e:
        print(f"❌ 解析响应异常: {str(e)}")
        print(f"异常响应内容: {response.text}")
    return None

def generate_signed_url(response_json):
    """根据API响应生成带签名的URL"""
    # 解析JSON响应
    data = json.loads(response_json.replace('&&&START&&&', ''))
    
    # 提取必要参数
    nonce = data['nonce']
    ssecurity = data['ssecurity']
    location = data['location']
    
    # 生成SHA1签名
    sign_data = f"nonce={nonce}&{ssecurity}"
    sha1_hash = hashlib.sha1(sign_data.encode('utf-8')).digest()
    signature = base64.b64encode(sha1_hash).decode('utf-8')
    # 修改这行
    url_encoded_sign = quote(signature)  # 直接使用导入的quote函数
    
    # 构造最终URL
    if '?' in location:
        signed_url = f"{location}&_userIdNeedEncrypt=true&clientSign={url_encoded_sign}"
    else:
        signed_url = f"{location}?_userIdNeedEncrypt=true&clientSign={url_encoded_sign}"
    
    return signed_url

# 在文件顶部添加
def read_accounts(file_path):
    """读取账号密码文件"""
    accounts = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '----' in line:
                    phone, pwd = line.split('----', 1)
                    accounts.append({'phone': phone, 'password': pwd})
    except Exception as e:
        print(f"❌ 读取账号文件失败: {str(e)}")
    return accounts

def save_cookie_to_file(file_path, formatted_cookie, login_data=None):
    """保存/更新cookie到文件"""
    phone = formatted_cookie.split('----')[0]
    new_lines = []
    
    try:
        # 读取现有cookie
        existing = {}
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if '----' in line:
                        p = line.split('----')[0]
                        existing[p] = line.strip()
        
        # 如果有额外的登录数据，添加到formatted_cookie中
        if login_data and 'tokens' in login_data:
            tokens = login_data['tokens']
            # 从响应中获取token信息
            pass_token = tokens.get('passToken', '')
            user_id = tokens.get('userId', '')
            p_security = tokens.get('psecurity', '')
            
            print("\n🔍 准备保存的token信息:")
            print(f"passToken: {pass_token}")
            print(f"userId: {user_id}")
            print(f"psecurity: {p_security}")
            
            # 构造额外的token信息字符串
            extra_info = f"passToken={pass_token}; userId={user_id}; psecurity={p_security}"
            formatted_cookie = f"{formatted_cookie}----{extra_info}"
            
            print("\n🔍 最终保存的完整信息:")
            print(formatted_cookie)
        
        # 更新当前账号的cookie
        existing[phone] = formatted_cookie
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            for cookie in existing.values():
                f.write(f"{cookie}\n")
                
        print(f"✅ 已更新 {phone} 的cookie到文件")
    except Exception as e:
        print(f"❌ 保存cookie失败: {str(e)}")
        print(f"异常详情: {str(e)}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")

# 修改main部分
if __name__ == '__main__':
    # 从账号文件读取
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))  # 获取脚本所在目录
    account_file = os.path.join(current_dir, "小米账号密码.txt")  # 账号文件路径
    cookie_file = os.path.join(current_dir, "小米视频cookies1.txt")  # cookie文件路径
    
    accounts = read_accounts(account_file)
    
    if not accounts:
        print("❌ 没有找到有效账号，请检查文件格式：手机号----密码")
        exit()
    
    for account in accounts:
        username = account['phone']
        password = account['password']
        print(f"\n====== 正在处理账号 {username} ======")
        
        login_data = get_xiaomi_cookie(username, password)
        if login_data and 'formatted_cookie' in login_data:
            save_cookie_to_file(cookie_file, login_data['formatted_cookie'], login_data)
            # 如果不是最后一个账号，则等待30秒
        if account != accounts[-1]:
            print("\n⏳ 等待30秒后处理下一个账号...")
            time.sleep(30)
