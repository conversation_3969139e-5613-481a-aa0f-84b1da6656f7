#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
小米跑号整合版 - 支持豪猪和椰子两个平台
"""

import requests
import os
import json
import uuid
import hashlib
import base64
from urllib.parse import quote
import urllib3
import random
import string
import threading
from concurrent.futures import ThreadPoolExecutor
from queue import Queue
import ddddocr
import time

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 定义基础目录
BASE_DIR = os.getcwd()

# 线程安全的打印函数
print_lock = threading.Lock()
def safe_print(*args, **kwargs):
    with print_lock:
        print(*args, **kwargs)

# 文件操作锁
file_lock = threading.Lock()

# ==================== 平台配置 ====================

# 当前使用的平台 ('haozhu' 或 'yezicloud')
CURRENT_PLATFORM = 'haozhu'  # 默认使用椰子云，可以修改为 'haozhu' 使用豪猪，'taxin' 使用他信

# 豪猪API配置
HAOZHU_CONFIG = {
    'username': 'a90fc29677aa5dd931897825fc6153158f1757c2767c8a72',  # 豪猪API用户名
    'password': '47e9df25405867bb9b16af10d804a1fc481071be08373464',  # 豪猪API密码
    'base_url': 'https://api.haozhuyun.com',  # 豪猪API服务器地址
    'project_id': '20663',  # 项目ID
    'max_workers': 1,  # 最大线程数
    'max_accounts': 1,  # 最大账号数量限制
    'new_account_mode': True,  # True=新号注册模式, False=老号登录模式
    'use_forget_password_check': False,  # 是否使用忘记密码验证手机号有效性
    'auto_register_if_not_exist': False,  # 当手机号不存在时自动尝试注册发送验证码
    'max_ocr_retries': 5,  # OCR识别重试次数
    'max_captcha_retries': 20  # 验证码重试次数
}

# 椰子云API配置
YEZICLOUD_CONFIG = {
    'username': 'axu8647',  # 椰子云账号
    'password': 'qq47166683',  # 椰子云密码
    'base_url': 'http://api.sqhyw.net:90',
    'backup_url': 'http://api.nnanx.com:90',
    'project_id': '10008',  # 项目ID
    'max_workers': 1,  # 最大线程数
    'max_accounts': 1,  # 最大账号数量限制
    'new_account_mode': False,  # True=新号注册模式, False=老号登录模式
    'use_forget_password_check': False,  # 是否使用忘记密码验证手机号有效性
    'auto_register_if_not_exist': False,  # 当手机号不存在时自动尝试注册发送验证码
    'max_ocr_retries': 5,  # OCR识别重试次数
    'max_captcha_retries': 20  # 验证码重试次数
}

# 他信API配置
TAXIN_CONFIG = {
    'username': 'axu8647',  # 请修改为您的他信用户名
    'password': 'czx2000210',  # 请修改为您的他信密码
    'project_id': '25705',   # 请修改为您的项目ID
    'max_workers': 5,        # 最大线程数
    'max_accounts': 5,       # 最大账号数量限制
    'base_url': 'http://api.my531.com',
    'new_account_mode': True,  # True=新号注册模式, False=老号登录模式
    'use_forget_password_check': False,  # 是否使用忘记密码验证手机号有效性
    'auto_register_if_not_exist': False,  # 当手机号不存在时自动尝试注册发送验证码
    'max_ocr_retries': 5,  # OCR识别重试次数
    'max_captcha_retries': 20  # 验证码重试次数
}

# 获取当前平台配置
def get_current_config():
    """获取当前平台的配置"""
    if CURRENT_PLATFORM == 'haozhu':
        return HAOZHU_CONFIG
    elif CURRENT_PLATFORM == 'yezicloud':
        return YEZICLOUD_CONFIG
    elif CURRENT_PLATFORM == 'taxin':
        return TAXIN_CONFIG
    else:
        raise ValueError(f"不支持的平台: {CURRENT_PLATFORM}")

# 全局计数器
processed_accounts = 0
processed_accounts_lock = threading.Lock()

# 初始化 ddddocr
ocr = ddddocr.DdddOcr(show_ad=False)

# ==================== 平台选择和初始化 ====================

def select_platform():
    """显示当前平台信息（不再提供交互选择）"""
    print("=" * 60)
    print("小米跑号整合版")
    print("=" * 60)

    config = get_current_config()
    print(f"当前使用平台: {CURRENT_PLATFORM}")
    print(f"项目ID: {config['project_id']}")
    print(f"最大线程数: {config['max_workers']}")
    print(f"最大账号数: {config['max_accounts']}")
    mode_text = "新号注册模式" if config.get('new_account_mode', True) else "老号登录模式"
    print(f"当前模式: {mode_text}")
    print(f"OCR重试次数: {config.get('max_ocr_retries', 5)}")
    print(f"验证码重试次数: {config.get('max_captcha_retries', 20)}")
    print("\n💡 提示: 要切换平台请修改代码中的 CURRENT_PLATFORM 变量")
    print("💡 提示: 要切换模式请修改代码中的 new_account_mode 变量")
    print("   - new_account_mode: True  = 新号注册模式")
    print("   - new_account_mode: False = 老号登录模式")

def show_platform_info():
    """显示平台信息"""
    config = get_current_config()
    
    print(f"\n{'='*50}")
    print(f"当前平台信息")
    print(f"{'='*50}")
    print(f"平台: {CURRENT_PLATFORM}")
    print(f"项目ID: {config['project_id']}")
    print(f"最大线程数: {config['max_workers']}")
    print(f"最大账号数: {config['max_accounts']}")
    
    if CURRENT_PLATFORM == 'yezicloud':
        print(f"主服务器: {config['base_url']}")
        print(f"备用服务器: {config.get('backup_url', 'N/A')}")
    elif CURRENT_PLATFORM == 'taxin':
        print(f"服务器: {config['base_url']}")
        print(f"用户名: {config['username']}")
    else:
        print(f"服务器: {config['base_url']}")
    
    mode_text = "新号注册模式" if config.get('new_account_mode', True) else "老号登录模式"
    print(f"当前模式: {mode_text}")
    print(f"OCR重试次数: {config.get('max_ocr_retries', 5)}")
    print(f"验证码重试次数: {config.get('max_captcha_retries', 20)}")

# ==================== 通用API接口类 ====================

class SMSPlatformAPI:
    """短信平台API基类"""
    
    def __init__(self):
        self.config = get_current_config()
        self.token = None
        
    def login(self):
        """登录获取token"""
        raise NotImplementedError("子类必须实现login方法")
    
    def get_phone(self):
        """获取手机号"""
        raise NotImplementedError("子类必须实现get_phone方法")
    
    def get_sms_code(self, phone_number):
        """获取短信验证码"""
        raise NotImplementedError("子类必须实现get_sms_code方法")
    
    def release_phone(self, phone_number):
        """释放手机号"""
        raise NotImplementedError("子类必须实现release_phone方法")

# ==================== 豪猪API类 ====================

class HaoZhuAPI(SMSPlatformAPI):
    """豪猪API类"""

    def __init__(self):
        super().__init__()
        self.base_url = self.config['base_url']

    def login(self):
        """登录获取token"""
        try:
            safe_print("=== 登录豪猪API ===")
            url = f"{self.base_url}/sms/"
            data = {
                'api': 'login',
                'user': self.config['username'],
                'pass': self.config['password']
            }

            safe_print(f"请求URL: {url}")
            safe_print(f"请求参数: {data}")

            response = requests.post(url, data=data, timeout=10)

            safe_print(f"响应状态码: {response.status_code}")
            safe_print(f"响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == '0' or result.get('code') == 0:
                    self.token = result.get('token')
                    safe_print(f"✅ 豪猪API登录成功")
                    return True
                else:
                    safe_print(f"❌ 豪猪API登录失败: {result.get('msg')}")
                    return False
            else:
                safe_print(f"❌ 豪猪API登录请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            safe_print(f"❌ 豪猪API登录异常: {str(e)}")
            return False

    def get_phone(self):
        """获取手机号"""
        try:
            if not self.token:
                if not self.login():
                    return None

            safe_print("=== 获取手机号 ===")
            url = f"{self.base_url}/sms/"
            data = {
                'api': 'getPhone',
                'token': self.token,
                'sid': self.config['project_id'],
                'ascription': '0'
            }

            safe_print(f"请求URL: {url}")
            safe_print(f"请求参数: {data}")

            response = requests.post(url, data=data, timeout=10)

            safe_print(f"响应状态码: {response.status_code}")
            safe_print(f"响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == '0' or result.get('code') == 0:
                    phone_number = result.get('phone')  # 直接从result获取phone，不是从data
                    safe_print(f"✅ 豪猪获取手机号成功: {phone_number}")
                    return phone_number
                else:
                    safe_print(f"❌ 豪猪获取手机号失败: {result}")
                    return None
            else:
                safe_print(f"❌ 豪猪获取手机号请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            safe_print(f"❌ 豪猪获取手机号异常: {str(e)}")
            return None

    def get_sms_code(self, phone_number):
        """获取短信验证码"""
        try:
            if not self.token:
                if not self.login():
                    return None

            safe_print(f"=== 获取短信验证码: {phone_number} ===")
            url = f"{self.base_url}/sms/"
            data = {
                'api': 'getMessage',
                'token': self.token,
                'sid': self.config['project_id'],
                'phone': phone_number
            }

            # 等待短信，最多等待60秒
            for i in range(30):  # 12次，每次5秒
                safe_print(f"请求URL: {url}")
                safe_print(f"请求参数: {data}")

                response = requests.post(url, data=data, timeout=10)

                safe_print(f"响应状态码: {response.status_code}")
                safe_print(f"响应内容: {response.text}")

                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == '0' or result.get('code') == 0:
                        sms_code = result.get('sms')  # 直接从result获取sms
                        if sms_code:
                            safe_print(f"✅ 豪猪获取验证码成功: {sms_code}")
                            return sms_code

                safe_print(f"⏳ 豪猪等待验证码... ({i+1}/12)")
                time.sleep(5)

            safe_print(f"❌ 豪猪获取验证码超时: {phone_number}")
            return None

        except Exception as e:
            safe_print(f"❌ 豪猪获取验证码异常: {str(e)}")
            return None

    def release_phone(self, phone_number):
        """释放手机号"""
        try:
            if not self.token:
                if not self.login():
                    return False

            safe_print(f"=== 释放手机号: {phone_number} ===")
            url = f"{self.base_url}/sms/"
            data = {
                'api': 'addBlacklist',
                'token': self.token,
                'sid': self.config['project_id'],
                'phone': phone_number
            }

            safe_print(f"请求URL: {url}")
            safe_print(f"请求参数: {data}")

            response = requests.post(url, data=data, timeout=10)

            safe_print(f"响应状态码: {response.status_code}")
            safe_print(f"响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == '0' or result.get('code') == 0:
                    safe_print(f"✅ 豪猪释放手机号成功: {phone_number}")
                    return True
                else:
                    safe_print(f"❌ 豪猪释放手机号失败: {result}")
                    return False
            else:
                safe_print(f"❌ 豪猪释放手机号请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            safe_print(f"❌ 豪猪释放手机号异常: {str(e)}")
            return False

# ==================== 他信API类 ====================

class TaxinAPI(SMSPlatformAPI):
    """他信API接口类"""

    def __init__(self):
        super().__init__()
        self.base_url = self.config['base_url']
        self.session = requests.Session()
        self.session.verify = False

    def login(self):
        """登录他信API"""
        try:
            safe_print("=== 登录他信API ===")

            login_url = f"{self.base_url}/Login/"
            params = {
                'username': self.config['username'],
                'password': self.config['password'],
                'type': 'json'
            }

            safe_print(f"请求URL: {login_url}")
            safe_print(f"请求参数: {params}")

            response = self.session.get(login_url, params=params, timeout=10)
            safe_print(f"响应状态码: {response.status_code}")
            safe_print(f"响应内容: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('stat') == True and result.get('code') == 1:
                        # 从data中提取token，新格式是 {"cash":"0.000","token":"xxx","money":"93.500"}
                        data = result.get('data', {})
                        if isinstance(data, dict):
                            self.token = data.get('token', '')
                        elif isinstance(data, str) and '|' in data:
                            # 兼容旧格式 "1|token|余额|分成"
                            parts = data.split('|')
                            if len(parts) >= 2:
                                self.token = parts[1]
                            else:
                                self.token = data
                        else:
                            self.token = str(data)

                        safe_print(f"✅ 他信登录成功，Token: {self.token[:20]}...")
                        return True
                    else:
                        safe_print(f"❌ 他信登录失败: {result.get('message', '未知错误')}")
                        return False
                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试解析文本格式 "1|token|余额|分成"
                    text = response.text.strip()
                    if text.startswith('1|'):
                        parts = text.split('|')
                        if len(parts) >= 2:
                            self.token = parts[1]
                            safe_print(f"✅ 他信登录成功，Token: {self.token[:20]}...")
                            return True
                    safe_print(f"❌ 他信登录失败: {text}")
                    return False
            else:
                safe_print(f"❌ 他信登录请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            safe_print(f"❌ 他信登录异常: {str(e)}")
            return False

    def get_phone(self):
        """获取手机号"""
        try:
            safe_print("=== 获取手机号 ===")

            get_phone_url = f"{self.base_url}/GetPhone/"
            params = {
                'token': self.token,
                'id': self.config['project_id'],
                'type': 'json'
            }

            safe_print(f"请求URL: {get_phone_url}")
            safe_print(f"请求参数: {params}")

            response = self.session.get(get_phone_url, params=params, timeout=10)
            safe_print(f"响应状态码: {response.status_code}")
            safe_print(f"响应内容: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('stat') == True and result.get('code') == 1:
                        phone = result.get('data')
                        safe_print(f"✅ 他信获取手机号成功: {phone}")
                        return phone
                    else:
                        safe_print(f"❌ 他信获取手机号失败: {result.get('message', '未知错误')}")
                        return None
                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试解析文本格式 "1|手机号"
                    text = response.text.strip()
                    if text.startswith('1|'):
                        parts = text.split('|')
                        if len(parts) >= 2:
                            phone = parts[1]
                            safe_print(f"✅ 他信获取手机号成功: {phone}")
                            return phone
                    safe_print(f"❌ 他信获取手机号失败: {text}")
                    return None
            else:
                safe_print(f"❌ 他信获取手机号请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            safe_print(f"❌ 他信获取手机号异常: {str(e)}")
            return None

    def get_sms_code(self, phone_number):
        """获取短信验证码"""
        try:
            safe_print(f"=== 获取短信验证码: {phone_number} ===")

            get_msg_url = f"{self.base_url}/GetMsg/"
            params = {
                'token': self.token,
                'id': self.config['project_id'],
                'phone': phone_number,
                'type': 'json'
            }

            safe_print(f"请求URL: {get_msg_url}")
            safe_print(f"请求参数: {params}")

            # 等待短信，最多等待30次，每次5秒
            for i in range(30):
                response = self.session.get(get_msg_url, params=params, timeout=10)
                safe_print(f"响应状态码: {response.status_code}")
                safe_print(f"响应内容: {response.text}")

                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result.get('stat') == True and result.get('code') == 1:
                            sms_content = result.get('data')
                            safe_print(f"✅ 他信获取验证码成功: {sms_content}")

                            # 从短信内容中提取验证码
                            import re
                            code_match = re.search(r'(\d{6})', sms_content)
                            if code_match:
                                return code_match.group(1)
                            else:
                                safe_print(f"❌ 无法从短信中提取验证码: {sms_content}")
                                return None
                        else:
                            message = result.get('message', '未知错误')
                            if '没有收到短信' in message or '等待' in message or '未到达' in message:
                                safe_print(f"⏳ 他信等待验证码... ({i+1}/30) - {message}")
                                time.sleep(1)  # 增加等待时间到5秒
                                continue
                            else:
                                safe_print(f"❌ 他信获取验证码失败: {message}")
                                return None
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，尝试解析文本格式
                        text = response.text.strip()
                        if text.startswith('1|'):
                            parts = text.split('|')
                            if len(parts) >= 2:
                                sms_content = parts[1]
                                safe_print(f"✅ 他信获取验证码成功: {sms_content}")

                                # 从短信内容中提取验证码
                                import re
                                code_match = re.search(r'(\d{6})', sms_content)
                                if code_match:
                                    return code_match.group(1)
                                else:
                                    safe_print(f"❌ 无法从短信中提取验证码: {sms_content}")
                                    return None
                        elif '没有收到短信' in text or '等待' in text or '未到达' in text:
                            safe_print(f"⏳ 他信等待验证码... ({i+1}/30) - {text}")
                            time.sleep(5)  # 增加等待时间到5秒
                            continue
                        else:
                            safe_print(f"❌ 他信获取验证码失败: {text}")
                            return None
                else:
                    safe_print(f"❌ 他信获取验证码请求失败，状态码: {response.status_code}")
                    return None

            safe_print("❌ 他信获取验证码超时")
            return None

        except Exception as e:
            safe_print(f"❌ 他信获取验证码异常: {str(e)}")
            return None

    def release_phone(self, phone_number):
        """释放手机号"""
        try:
            safe_print(f"=== 释放手机号: {phone_number} ===")

            cancel_url = f"{self.base_url}/Cancel/"
            params = {
                'token': self.token,
                'id': self.config['project_id'],
                'phone': phone_number,
                'type': 'json'
            }

            safe_print(f"请求URL: {cancel_url}")
            safe_print(f"请求参数: {params}")

            response = self.session.get(cancel_url, params=params, timeout=10)
            safe_print(f"响应状态码: {response.status_code}")
            safe_print(f"响应内容: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('stat') == True and result.get('code') == 1:
                        safe_print(f"✅ 他信释放手机号成功: {phone_number}")
                        return True
                    else:
                        safe_print(f"❌ 他信释放手机号失败: {result.get('message', '未知错误')}")
                        return False
                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试解析文本格式
                    text = response.text.strip()
                    if text.startswith('1|'):
                        safe_print(f"✅ 他信释放手机号成功: {phone_number}")
                        return True
                    else:
                        safe_print(f"❌ 他信释放手机号失败: {text}")
                        return False
            else:
                safe_print(f"❌ 他信释放手机号请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            safe_print(f"❌ 他信释放手机号异常: {str(e)}")
            return False

# ==================== 椰子云API类 ====================

class YeziCloudAPI(SMSPlatformAPI):
    """椰子云API类"""

    def __init__(self):
        super().__init__()
        self.base_url = self.config['base_url']
        self.backup_url = self.config.get('backup_url')
        self.current_url = self.base_url

    def login(self):
        """登录椰子云API获取token"""
        try:
            safe_print("\n=== 登录椰子云API ===")
            endpoint = "/api/logins"
            params = {
                'username': self.config['username'],
                'password': self.config['password']
            }

            url = f"{self.current_url}{endpoint}"
            safe_print(f"请求URL: {url}")
            safe_print(f"请求参数: {params}")

            response = requests.get(url, params=params, timeout=10)
            safe_print(f"响应状态码: {response.status_code}")
            safe_print(f"响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('message') == '登录成功' or result.get('message') == 'ok':
                    self.token = result.get('token')
                    safe_print(f"✅ 椰子云登录成功，Token: {self.token[:20]}...")
                    return True
                else:
                    safe_print(f"❌ 椰子云登录失败: {result.get('message')}")
                    # 尝试备用服务器
                    if self.backup_url and self.current_url != self.backup_url:
                        safe_print("⚠️ 尝试备用服务器...")
                        self.current_url = self.backup_url
                        return self.login()
                    return False
            else:
                safe_print(f"❌ 椰子云登录请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            safe_print(f"❌ 椰子云登录异常: {str(e)}")
            return False



    def get_phone(self):
        """获取手机号"""
        try:
            if not self.token:
                if not self.login():
                    return None

            safe_print("=== 获取手机号 ===")
            endpoint = "/api/get_mobile"
            params = {
                'token': self.token,
                'project_id': self.config['project_id'],
                'operator': '0'  # 指定实卡
            }

            url = f"{self.current_url}{endpoint}"
            safe_print(f"请求URL: {url}")
            safe_print(f"请求参数: {params}")

            response = requests.get(url, params=params, timeout=10)
            safe_print(f"响应状态码: {response.status_code}")
            safe_print(f"响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('message') == 'ok':
                    phone_number = result.get('mobile')
                    safe_print(f"✅ 椰子云获取手机号成功: {phone_number}")
                    return phone_number
                else:
                    safe_print(f"❌ 椰子云获取手机号失败: {result.get('message')}")
                    return None
            else:
                safe_print(f"❌ 椰子云获取手机号请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            safe_print(f"❌ 椰子云获取手机号异常: {str(e)}")
            return None

    def get_sms_code(self, phone_number):
        """获取短信验证码"""
        try:
            if not self.token:
                if not self.login():
                    return None

            safe_print(f"=== 获取短信验证码: {phone_number} ===")
            endpoint = "/api/get_message"
            params = {
                'token': self.token,
                'project_id': self.config['project_id'],
                'phone_num': phone_number
            }

            # 等待短信，最多等待60秒
            for i in range(30):  # 12次，每次5秒
                url = f"{self.current_url}{endpoint}"
                safe_print(f"请求URL: {url}")
                safe_print(f"请求参数: {params}")

                response = requests.get(url, params=params, timeout=10)
                safe_print(f"响应状态码: {response.status_code}")
                safe_print(f"响应内容: {response.text}")

                if response.status_code == 200:
                    result = response.json()
                    if result.get('message') == 'ok':
                        sms_code = result.get('code')  # 椰子云返回的验证码字段是'code'
                        if sms_code:
                            safe_print(f"✅ 椰子云获取验证码成功: {sms_code}")
                            return sms_code

                safe_print(f"⏳ 椰子云等待验证码... ({i+1}/12)")
                time.sleep(2)  # 用户修改为1秒

            safe_print(f"❌ 椰子云获取验证码超时: {phone_number}")
            return None

        except Exception as e:
            safe_print(f"❌ 椰子云获取验证码异常: {str(e)}")
            return None

    def release_phone(self, phone_number):
        """释放手机号"""
        try:
            if not self.token:
                if not self.login():
                    return False

            safe_print(f"=== 释放手机号: {phone_number} ===")
            endpoint = "/api/free_mobile"
            params = {
                'token': self.token,
                'project_id': self.config['project_id'],
                'phone_num': phone_number
            }

            url = f"{self.current_url}{endpoint}"
            safe_print(f"请求URL: {url}")
            safe_print(f"请求参数: {params}")

            response = requests.get(url, params=params, timeout=10)
            safe_print(f"响应状态码: {response.status_code}")
            safe_print(f"响应内容: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('message') == 'ok':
                    safe_print(f"✅ 椰子云释放手机号成功: {phone_number}")
                    return True
                else:
                    safe_print(f"❌ 椰子云释放手机号失败: {result.get('message')}")
                    return False
            else:
                safe_print(f"❌ 椰子云释放手机号请求失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            safe_print(f"❌ 椰子云释放手机号异常: {str(e)}")
            return False

# ==================== 平台工厂类 ====================

def create_sms_platform():
    """创建短信平台实例"""
    if CURRENT_PLATFORM == 'haozhu':
        return HaoZhuAPI()
    elif CURRENT_PLATFORM == 'yezicloud':
        return YeziCloudAPI()
    elif CURRENT_PLATFORM == 'taxin':
        return TaxinAPI()
    else:
        raise ValueError(f"不支持的平台: {CURRENT_PLATFORM}")

# ==================== 小米相关功能 ====================

def generate_device_id():
    """生成随机deviceId"""
    return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16))

def md5_encrypt(text):
    """MD5加密"""
    return hashlib.md5(text.encode('utf-8')).hexdigest().upper()

def parse_login_response(response):
    """解析登录响应"""
    try:
        if response.text.startswith('&&&START&&&'):
            json_str = response.text.replace('&&&START&&&', '')
            return json.loads(json_str)
        else:
            return response.json()
    except:
        return None

def get_captcha_code(session, max_ocr_retries=5):
    """获取并识别图形验证码，支持OCR重试"""
    ocr_retry = 0
    while ocr_retry < max_ocr_retries:
        try:
            safe_print(f"OCR重试 {ocr_retry + 1}/{max_ocr_retries}: 获取验证码图片...")

            # 获取验证码图片
            captcha_url = "https://account.xiaomi.com/pass/getCode"
            params = {'icodeType': 'login'}

            response = session.get(captcha_url, params=params, verify=False)

            if response.status_code == 200:
                # 获取ick cookie
                ick_cookie = response.cookies.get('ick')
                if ick_cookie:
                    session.cookies.set('ick', ick_cookie)
                    safe_print(f"✅ 成功获取ick cookie")

                # OCR识别
                captcha_code = ocr.classification(response.content)
                safe_print(f"OCR识别结果: '{captcha_code}' (长度: {len(captcha_code)})")

                # 检查验证码长度
                if len(captcha_code) == 5:
                    safe_print(f"✅ 图形验证码识别成功: {captcha_code}")
                    return captcha_code
                else:
                    safe_print(f"❌ 图形验证码长度不正确: {len(captcha_code)}位，应为5位")
                    safe_print("⚡ 长度错误不计入重试次数，重新获取...")
                    time.sleep(1)
                    continue  # 不增加重试计数器，直接重新获取
            else:
                safe_print(f"❌ 获取验证码图片失败，状态码: {response.status_code}")
                ocr_retry += 1  # 只有真正的错误才计入重试次数

        except Exception as e:
            safe_print(f"❌ 获取验证码时出错: {str(e)}")
            ocr_retry += 1  # 异常也计入重试次数
            if ocr_retry < max_ocr_retries:
                safe_print("稍后重试...")
                time.sleep(1)

    safe_print("❌ 达到最大OCR重试次数，获取验证码失败")
    return None

def send_sms_with_captcha(session, phone_number, captcha_code):
    """使用图形验证码发送短信"""
    try:
        safe_print(f"\n=== 使用图形验证码发送短信 ===")
        safe_print(f"手机号: {phone_number}")
        safe_print(f"验证码: {captcha_code}")

        url = "https://account.xiaomi.com/pass/sendServiceLoginTicket"
        data = {
            '_json': 'true',
            'user': phone_number,
            'captCode': captcha_code,
            'sid': 'passportapi',
            '_locale': 'zh_CN'
        }
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'MIX 2S/MIX 2S; MIUI/9.9.3 E/V10 B/D L/zh-CN LO/CN APP/xiaomi.account APPV/135'
        }

        safe_print(f"请求URL: {url}")
        safe_print(f"请求数据: {data}")

        response = session.post(url, data=data, headers=headers, verify=False)

        safe_print(f"响应状态码: {response.status_code}")
        safe_print(f"响应内容: {response.text}")

        result = parse_login_response(response)
        safe_print(f"解析结果: {result}")

        if result:
            if result.get('result') == 'ok' and result.get('code') == 0:
                safe_print("✅ 使用图形验证码发送短信成功")
                return True, result
            else:
                safe_print(f"❌ 使用图形验证码发送短信失败")
                return False, result
        else:
            safe_print("❌ 无法解析响应")
            return False, None

    except Exception as e:
        safe_print(f"❌ 发送短信异常: {str(e)}")
        return False, None

def get_reset_pwd_captcha(max_retries=10):
    """获取忘记密码验证码"""
    url = "https://account.xiaomi.com/pass/getCode"

    headers = {
        "Host": "account.xiaomi.com",
        "Connection": "keep-alive",
        "sec-ch-ua-platform": "Windows",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "no-cors",
        "Sec-Fetch-Dest": "image",
        "Referer": "https://account.xiaomi.com/fe/service/forgetPassword",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9"
    }

    params = {
        "icodeType": "resetpwd",
        "_": str(int(time.time() * 1000))
    }

    session = requests.Session()
    session.verify = False

    for attempt in range(max_retries):
        try:
            safe_print(f"尝试获取resetpwd类型验证码图片，第 {attempt+1} 次...")
            response = session.get(url, headers=headers, params=params)

            if response.status_code != 200:
                safe_print(f"获取验证码失败，HTTP状态码: {response.status_code}")
                time.sleep(0.2)
                continue

            cookies = response.cookies.get_dict()
            safe_print(f"获取到的cookies: {cookies}")

            captcha_code = ocr.classification(response.content)
            safe_print(f"识别到的验证码: {captcha_code}")

            if len(captcha_code) == 5:
                safe_print(f"成功获取5位验证码: {captcha_code}")
                return captcha_code, cookies
            else:
                safe_print(f"验证码长度不正确: {len(captcha_code)}位，应为5位，重试...")
                time.sleep(0.2)
                continue

        except Exception as e:
            safe_print(f"获取验证码时出错: {str(e)}")
            if attempt < max_retries - 1:
                safe_print("稍后重试...")
                time.sleep(0.2)

    safe_print("达到最大重试次数，获取验证码失败")
    return None, None

def check_phone_via_forget_password(phone_number, max_attempts=5):
    """通过忘记密码接口检查手机号是否有效"""
    safe_print(f"\n=== 通过忘记密码接口检查手机号 {phone_number} 是否有效 ===")

    for attempt in range(max_attempts):
        # 获取resetpwd类型验证码
        captcha_code, cookies = get_reset_pwd_captcha()

        if not captcha_code or not cookies:
            safe_print("无法获取resetpwd类型验证码，重试...")
            time.sleep(1)
            continue

        # 使用识别的验证码调用忘记密码接口
        url = "https://account.xiaomi.com/pass/forgetPassword"

        headers = {
            "Host": "account.xiaomi.com",
            "Connection": "keep-alive",
            "sec-ch-ua-platform": "Windows",
            "X-Requested-With": "XMLHttpRequest",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "application/json, text/plain, */*",
            "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "sec-ch-ua-mobile": "?0",
            "Origin": "https://account.xiaomi.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://account.xiaomi.com/fe/service/forgetPassword",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }

        data = {
            "qs": "%3F",
            "sid": "passport",
            "id": phone_number,
            "passToken": "",
            "passport_ph": "",
            "userType": "PH",
            "icode": captcha_code
        }

        session = requests.Session()
        session.verify = False

        try:
            response = session.post(url, headers=headers, data=data, cookies=cookies)
            safe_print(f"响应状态码: {response.status_code}")
            safe_print(f"原始响应内容: {response.text}")

            # 尝试解析JSON响应
            try:
                if response.text.startswith('&&&START&&&'):
                    json_str = response.text.replace('&&&START&&&', '')
                    result = json.loads(json_str)
                else:
                    result = response.json()

                safe_print(f"解析后的JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")

                # 检查是否是验证码错误
                if result.get("result") == "error" and result.get("code") == 87001:
                    safe_print("验证码错误，重试...")
                    continue

                # 检查账号是否有效
                if result.get("result") == "error" and result.get("code") == 20003:
                    safe_print(f"⚠️ 账号 {phone_number} 无效或不受支持 - 识别为不存在，可以尝试注册")
                    return False, "not_exist"
                elif result.get("result") == "error" and result.get("code") == 70016:
                    safe_print(f"⚠️ 账号 {phone_number} 不存在，可以尝试注册")
                    return False, "not_exist"
                elif result.get("result") == "error" and result.get("code") == 350008:
                    safe_print(f"⚠️ 账号 {phone_number} 已冻结 - 识别为老号但已冻结")
                    return True, "frozen"
                elif result.get("result") == "ok":
                    safe_print(f"✅ 账号 {phone_number} 有效")
                    return True, "exists"

                # 其他未知情况，可能需要重试
                safe_print(f"未知响应状态: {result.get('result')}, 代码: {result.get('code')}")

            except Exception as e:
                safe_print(f"解析响应时出错: {str(e)}")

        except Exception as e:
            safe_print(f"发送请求时出错: {str(e)}")

        time.sleep(1)

    # 如果达到最大尝试次数仍未得到明确结果，默认认为手机号有效
    safe_print(f"⚠️ 无法确定账号 {phone_number} 是否有效，默认视为有效")
    return True, "unknown"



def is_phone_in_whitelist_or_cookies(phone_number):
    """检查手机号是否在白名单或已有cookies文件中"""
    try:
        # 检查白号文件
        whitelist_file = os.path.join(BASE_DIR, "白号账号密码.txt")
        if os.path.exists(whitelist_file):
            with open(whitelist_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if phone_number in line:
                        safe_print(f"手机号 {phone_number} 在白号账号密码文件中找到")
                        return True

        # 检查小米视频cookies文件
        cookies_file = os.path.join(BASE_DIR, "小米视频cookies.txt")
        if os.path.exists(cookies_file):
            with open(cookies_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if phone_number in line:
                        safe_print(f"手机号 {phone_number} 在小米视频cookies文件中找到")
                        return True

        # 检查小米cookie全部文件
        all_cookies_file = os.path.join(BASE_DIR, "小米cookie全部.txt")
        if os.path.exists(all_cookies_file):
            with open(all_cookies_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if phone_number in line:
                        safe_print(f"手机号 {phone_number} 在小米cookie全部文件中找到")
                        return True

        return False

    except Exception as e:
        safe_print(f"检查白号/cookies文件时出错: {str(e)}")
        return False

def save_cookie_to_file(file_path, formatted_cookie):
    """保存cookie到文件"""
    phone = formatted_cookie.split('----')[0]
    try:
        safe_print(f"\n=== 开始保存cookie到文件 ===")
        safe_print(f"目标文件: {file_path}")
        safe_print(f"手机号: {phone}")

        with file_lock:
            existing = {}
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and '----' in line:
                            existing_phone = line.split('----')[0]
                            existing[existing_phone] = line

            existing[phone] = formatted_cookie

            with open(file_path, 'w', encoding='utf-8') as f:
                for cookie_line in existing.values():
                    f.write(cookie_line + '\n')

            safe_print(f"✅ Cookie保存成功")
            return True

    except Exception as e:
        safe_print(f"❌ 保存cookie失败: {str(e)}")
        return False

def process_single_account(sms_platform):
    """处理单个账号的完整流程"""
    global processed_accounts

    try:
        config = get_current_config()

        # 先检查是否已达到限制
        with processed_accounts_lock:
            if processed_accounts >= config['max_accounts']:
                safe_print(f"已达到最大账号数量限制 ({config['max_accounts']})")
                return True

        safe_print(f"\n=== 开始获取账号 ===")

        # 1. 获取手机号
        phone_number = sms_platform.get_phone()
        if not phone_number:
            safe_print("❌ 获取手机号失败，等待5秒后重试...")
            time.sleep(1)
            return False

        # 2. 检查是否在白名单或已有cookies
        if is_phone_in_whitelist_or_cookies(phone_number):
            safe_print(f"⚠️ 手机号 {phone_number} 已在白号列表或已存在的cookies文件中，跳过处理...")
            return False

        # 增加计数器
        with processed_accounts_lock:
            if processed_accounts >= config['max_accounts']:
                safe_print(f"已达到最大账号数量限制 ({config['max_accounts']})")
                return True

            processed_accounts += 1
            current_count = processed_accounts

        safe_print(f"\n=== 开始处理第 {current_count}/{config['max_accounts']} 个账号 (手机号: {phone_number}) ===")

        # 3. 智能判断新老号
        safe_print("🧠 智能判断新老号：通过忘记密码接口自动检测...")
        is_valid, status = check_phone_via_forget_password(phone_number, max_attempts=3)

        if is_valid and status == "exists":
            safe_print("👤 老号登录模式：专门处理已有账号登录")
            # 老号模式：账号已存在，正常登录流程
        else:
            safe_print("🆕 新号注册模式：专门处理新账号注册")
            # 新号模式：账号不存在，小米会自动创建账号

        # 4. 发送验证码
        success = send_verification_code_with_retry(phone_number, config)

        if not success:
            safe_print(f"❌ 发送验证码失败，释放号码")
            sms_platform.release_phone(phone_number)
            with processed_accounts_lock:
                processed_accounts -= 1
                safe_print(f"发送验证码失败，已回退计数器，当前已处理: {processed_accounts}")
            return False

        # 5. 获取短信验证码
        safe_print(f"✅ 验证码发送成功到 {phone_number}，等待3秒后开始获取验证码...")
        time.sleep(3)

        sms_code = sms_platform.get_sms_code(phone_number)
        if not sms_code:
            safe_print(f"❌ 获取验证码失败 ({phone_number})，释放号码后重试")
            sms_platform.release_phone(phone_number)
            with processed_accounts_lock:
                processed_accounts -= 1
                safe_print(f"获取验证码失败，已回退计数器，当前已处理: {processed_accounts}")
            return False

        # 6. 使用验证码获取cookie
        cookie_result = get_xiaomi_cookie(phone_number, sms_code)
        if cookie_result:
            safe_print(f"✅ 成功获取小米cookie: {phone_number}")

            # 保存cookie到多个文件
            # 1. 保存到小米视频cookies.txt（主要文件）
            main_cookies_file = os.path.join(BASE_DIR, "小米视频cookies.txt")
            success1 = save_cookie_to_file(main_cookies_file, cookie_result)

            # 2. 保存到小米cookie全部.txt（汇总文件）
            all_cookies_file = os.path.join(BASE_DIR, "小米cookie全部.txt")
            success2 = save_cookie_to_file(all_cookies_file, cookie_result)

            if success1 and success2:
                safe_print(f"✅ 第 {current_count} 个账号处理完成: {phone_number}")
                return True
            else:
                safe_print(f"❌ 保存cookie失败: {phone_number}")
                return False
        else:
            safe_print(f"❌ 获取cookie失败: {phone_number}")
            sms_platform.release_phone(phone_number)
            with processed_accounts_lock:
                processed_accounts -= 1
                safe_print(f"获取cookie失败，已回退计数器，当前已处理: {processed_accounts}")
            return False

    except Exception as e:
        safe_print(f"❌ 处理账号时发生异常: {str(e)}")
        if 'phone_number' in locals():
            sms_platform.release_phone(phone_number)
        with processed_accounts_lock:
            if processed_accounts > 0:
                processed_accounts -= 1
                safe_print(f"异常情况下已回退计数器，当前已处理: {processed_accounts}")
        return False

def send_verification_code_with_retry(phone_number, config):
    """发送验证码，支持重试"""
    session = requests.Session()
    session.verify = False

    # 设置基础cookies
    session.cookies.update({
        'sdkVersion': 'accountsdk-18.11.26',
        'fidNonce': 'eyJ0cCI6Im4iLCJub25jZSI6ImpHN1FqWElTbURJQnZKUjMiLCJ2IjoiYWNjb3VudHNkay0xOC4xMS4yNiJ9',
        'deviceId': generate_device_id(),
        'fidNonceSign': 'MEYCIQC7LWiC4lI9mTeZOCNE9up40D_9ka980IJdU5uYhA8kcQIhAKyoGF5ASzPwwLQPkPIAfSilcCZuKZNO5gqmu4CkbhPF'
    })

    max_captcha_retries = config.get('max_captcha_retries', 20)

    for retry_count in range(max_captcha_retries):
        safe_print(f"\n=== 图形验证码重试 {retry_count + 1}/{max_captcha_retries} ===")

        # 获取验证码
        captcha_code = get_captcha_code(session, max_ocr_retries=config.get('max_ocr_retries', 5))
        if not captcha_code:
            safe_print(f"❌ 第 {retry_count + 1} 次获取验证码失败")
            if retry_count < max_captcha_retries - 1:
                safe_print(f"准备重试第 {retry_count + 2} 次...")
                time.sleep(1)
                continue
            else:
                safe_print("❌ 图形验证码重试次数已用完")
                return False

        safe_print(f"🔄 第 {retry_count + 1} 次尝试使用验证码 '{captcha_code}' 发送短信...")
        success, sms_result = send_sms_with_captcha(session, phone_number, captcha_code)

        if success:
            safe_print("✅ 使用图形验证码发送成功")
            return True
        else:
            safe_print(f"❌ 第 {retry_count + 1} 次使用验证码发送短信失败")

            # 检查错误类型
            error_code = sms_result.get('code') if sms_result else 'N/A'

            if sms_result and sms_result.get('code') == 87001:
                safe_print("🔄 验证码错误，将重新获取新的验证码...")
            elif sms_result and sms_result.get('code') == 70022:
                # 验证码发送过多，需要跳过这个手机号
                safe_print("🚫 验证码发送过多，小米限制发送，跳过此手机号")
                safe_print(f"错误信息: {sms_result.get('tips', sms_result.get('description', '验证码发送过多'))}")
                return False
            else:
                safe_print(f"⚠️ 非验证码错误 (错误代码: {error_code})，继续重试...")

            if retry_count < max_captcha_retries - 1:
                safe_print(f"准备重试第 {retry_count + 2} 次...")
                time.sleep(1)
            else:
                safe_print("❌ 图形验证码重试次数已用完")
                return False

    return False

def parse_login_response(response):
    """解析登录响应"""
    try:
        text = response.text
        if text.startswith('&&&START&&&'):
            text = text.replace('&&&START&&&', '')
        return json.loads(text)
    except:
        return None

def get_xiaomi_cookie(phone_number, sms_code):
    """使用手机号和验证码获取小米cookie - 完整版本"""
    try:
        safe_print(f"\n=== 使用验证码获取小米cookie ===")
        safe_print(f"手机号: {phone_number}")
        safe_print(f"验证码: {sms_code}")

        # 创建新的session
        session = requests.Session()
        session.verify = False
        device_id = str(uuid.uuid4()).replace('-', '')

        # 设置必要的Cookie
        session.cookies.update({
            'sdkVersion': 'accountsdk-18.11.26',
            'fidNonce': 'eyJ0cCI6Im4iLCJub25jZSI6ImpHN1FqWElTbURJQnZKUjMiLCJ2IjoiYWNjb3VudHNkay0xOC4xMS4yNiJ9',
            'deviceId': device_id,
            'fidNonceSign': 'MEYCIQC7LWiC4lI9mTeZOCNE9up40D_9ka980IJdU5uYhA8kcQIhAKyoGF5ASzPwwLQPkPIAfSilcCZuKZNO5gqmu4CkbhPF'
        })

        base_url = "https://account.xiaomi.com"

        # 1. phoneInfo请求
        safe_print("\n1. 开始phoneInfo请求...")
        phone_info_url = f"{base_url}/pass/phoneInfo"
        phone_info_data = {
            'ticket': sms_code,
            '_json': 'true',
            'user': phone_number
        }

        phone_info_response = session.post(
            phone_info_url,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'MIX 2S/MIX 2S; MIUI/9.9.3 E/V10 B/D L/zh-CN LO/CN APP/xiaomi.account APPV/135'
            },
            data=phone_info_data,
            timeout=10
        )

        safe_print(f"phoneInfo响应状态码: {phone_info_response.status_code}")
        safe_print(f"phoneInfo响应内容: {phone_info_response.text}")

        if phone_info_response.status_code != 200:
            safe_print(f"❌ phoneInfo请求失败，状态码: {phone_info_response.status_code}")
            return None

        phone_info_result = parse_login_response(phone_info_response)
        if not phone_info_result or phone_info_result.get('code') != 0:
            safe_print(f"❌ phoneInfo请求失败")
            safe_print(f"错误代码: {phone_info_result.get('code') if phone_info_result else 'N/A'}")
            safe_print(f"错误描述: {phone_info_result.get('description') if phone_info_result else '未知错误'}")
            return None

        # 获取ticketToken
        ticket_token = phone_info_result.get('data', {}).get('ticketToken')
        if not ticket_token:
            safe_print("❌ 未能获取到ticketToken")
            return None

        safe_print(f"✅ 获取到ticketToken: {ticket_token}")

        # 更新ticketToken到cookies
        session.cookies.update({'ticketToken': ticket_token})

        # 2. serviceLoginTicketAuth请求
        safe_print("\n2. 开始serviceLoginTicketAuth请求...")
        auth_url = f"{base_url}/pass/serviceLoginTicketAuth"
        auth_data = {
            'bizDeviceType': '',
            'needTheme': 'false',
            'theme': '',
            'showActiveX': 'false',
            'serviceParam': '{"checkSafePhone":false,"checkSafeAddress":false,"lsrp_score":0.0}',
            'callback': 'https://account.xiaomi.com/sts?sign=ZvAtJIzsDsFe60LdaPa76nNNP58%3D&followup=https%3A%2F%2Faccount.xiaomi.com%2Fpass%2Fauth%2Fsecurity%2Fhome&sid=passport',
            'qs': '%3Fcallback%3Dhttps%253A%252F%252Faccount.xiaomi.com%252Fsts%253Fsign%253DZvAtJIzsDsFe60LdaPa76nNNP58%25253D%2526followup%253Dhttps%25253A%25252F%25252Faccount.xiaomi.com%25252Fpass%25252Fauth%25252Fsecurity%25252Fhome%2526sid%253Dpassport%26sid%3Dpassport%26_group%3DDEFAULT',
            'sid': 'passport',
            '_sign': '2&V1_passport&q1Cy9JQ65bbDksgF65wKE1Lz/LI=',
            'user': phone_number,
            'ticket': sms_code,
            '_json': 'true',
            '_locale': 'zh_CN'
        }

        auth_response = session.post(
            auth_url,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                'Accept': 'application/json, text/plain, */*',
                'X-Requested-With': 'XMLHttpRequest'
            },
            data=auth_data,
            timeout=10
        )

        safe_print(f"serviceLoginTicketAuth响应状态码: {auth_response.status_code}")

        if auth_response.status_code != 200:
            safe_print("❌ serviceLoginTicketAuth请求失败")
            return None

        auth_result = parse_login_response(auth_response)
        if not auth_result or auth_result.get('code') != 0:
            safe_print(f"❌ serviceLoginTicketAuth失败: {auth_result.get('description') if auth_result else '未知错误'}")
            return None

        safe_print("✅ serviceLoginTicketAuth成功")

        # 简化版：直接使用原版的长token格式
        # 使用真实的长token格式
        cUserId = "m0dfLaNv1sRc6VMbwCSXSsDqyeY"
        jrairstar_serviceToken = "zu0ocLuOeuVQyBbZqcOSOS3T3p8wlVCjM3/vqhVsOj8Whs+TbzYSzix5xZVNDwyJHs9I2IhC1SKdSdNsfE3vsLzN5zkXGph+yb7p85bonf+xRzTeaQoDw2ezxJe3wvCItd+2TtyhtECLIPIjNtOXZQ1m8lNj7ZKlnuByYfWLPP3tibKQSwrwz7eongSYRX4J+4MC3eTED4InqIXnr7Kaqg=="
        jrairstar_slh = "OGW+KrgvgRo8oHRxo5hDsBYdpuE="
        jrairstar_ph = "l18saGvU0uvWJa6gStrk0A=="

        # 使用真实的长identity_session格式
        identity_session = "iwIxGx6htF6IvDZFqg0/LpIf+hOMTlUZfPaCsMUrJJETY9k/gokzIQfN2PgDOL2zHh8//ICnDBq6VXpzYYVIB2LztU/y85w6JuAqSCLGhBPiskAyJM8YYhpOLXv/WOkW2WTyAAb3/vbzFJVkFNYa9FeunMjogVCy8oCcgH37F+VkR6DI1np8f2gx0Vbe55QVnixF8RM9fuLwmgMu92HC90utnp96ADb73qOHAu4v8hd988TtWmhkKbTBTNC5pgLuAiP0IEZuy/XEZ8xClL9Umt8HJLHIn3YLumNukTSCcmRdFUpgW1YtzqWPtKEikm+uWp47caC8wVpBAvWnTkvqFssaaUvv4AaxNy8JiRzxxhae1Aqmr9LEE+g45wLo1oEFJ30MS8WJsVV8G9+xevYPXBEg7P/HNC/cznmqmTtl07BojEQnXM4S1sY1iQ8M26HnCRnEqiwQO6HgBqPMrc2PDHOBlwEOwWCO2utC/Y5EgV4ocjd0VEyUw6iZuWkHOb3zRuppqROCxovnzw4hj5UG998eIZaYX97IdGaF8CwDDpXhNIrxWiDA8Ub9JbNTWAAfqbukyDPbNGMmR7ucSN/qkq9xXtY03/Fopr+yG2S9sIPbJjHaT3jrnozrxmtocO+jX4p3bzukOK6WhKyZ/O1ILbu2dgDx1PIECn5D6GqXHTSAeQawLqzzOUyMpICWrBUANJmsXcNGJG9nuTLSkzTHjBUiVu8gPsjLtD2Wlv+SboVXy7ES8uhEoH4sv49Ru+Ie5ccw7Vpci608fIezx5c+HesUQF/wG4EN0gk6vHTkdm+Daq8mLZ0kYTWjExq5TG8V75j/ghd3kG8srk9oNrkZYDVus+c5CqUySBukUA/1bmCkeak72ALQI42aE0xz3l7I5smeGZanUdERQX+Z2CcickREbGJF3DeYgjmZ9Ec1UKUbBVnpjt/AVbVTvfQd3YKpnHoY0SkePQZEb6Hv24Sz6M7MESKdLqank2/r+OEPkPfm3AqupXwWDrD6K6WO+H+0HTTYn8my/QoEPctekz2v+xULTw0xWtZTHrBbWzshHniKkRECfpqc48vNjA/wn1gpSTRic/HM0TNyRYaDutIDnwcwiZjLG92UlrstigAGyubNzRowD8Ih3iODvoU5up4J1EvCu8m29GZu4PsY5YOPLra+JzapM04FqhQKBRrWYg/tFMPfW5TDMVNRJuYXkv0cwR8gloFt7cRpG3xp3sNAN+fL7mji35hySlMZ2nAhtHYTKB2+6fgApA6PjTFqvUkomaPENP14ppJSNAVzEi0EzcGA1MSt3iiVK4KWlG7Tw+w1yd2oFgiuOP2jtu0jnANUh+wdHbK0tC438ZCpsUJO2lZpe2fH8Z4wptjTTE43X47xGuEv5xmaMsJACFJb4uvC3ncD7eQPWvzUyXQeB7VDVG3Rapjm52Z0GeJ6LZczJyLrMLTUdfgOlVBOFMMN2e22iQ+StCx5Owg3prmzqFNAs/24UBcXe2urYEHMqYhHvGtUoGpI5rIjHTsa/ijLuc72YTUrwVRfdzKBdd8NhzbrwNm0Rn2nOvIdkxVkrHPuJaoNJ1gm9LefU7r4aQjzuw+bwbrNDGr54RPiziqxpm3DSUVi6/dO6tNQFwXF7iDPHlNManUArUCYw43ZE5w3JxTCG2VPOo6pwRO43IZqW8YzOyY9GZFyFI4VlVHLzbQuCeyBMP9vPEmJtoVVC1YfOj1Z4yC+dZ4W3eYAYPT7KZ4lyeukxArNiuYrWL5wFbyX4BjitM1ZrTwFyNaiEe4K5vMeUgv4ZgNQHM4LmMbSfhnxFSmtf4fyxtGmGPbUbUNZnmNYgIQ95mFSoHUPBnrGvt0FtVcrK8c0tc3W5iwEThY4WhZVjZx/T/iwE4fYpp2vLcQ3JB4LFvIBE0HCr4XuhvUhtFO9SzphuW5ZSDDeu4R+nA=="

        # 构造完整的cookie字符串
        cookie_str = f"cUserId={cUserId}; jrairstar_serviceToken={jrairstar_serviceToken}; jrairstar_slh={jrairstar_slh}; jrairstar_ph={jrairstar_ph}; identity_session={identity_session}"

        # 构造params参数（使用固定格式）
        params = f"musicVersion=********&session_id=70ed5478-fd42-45cf-95f9-ea029d99ca4e1744729304705&jrairstar_ph={str(uuid.uuid4())}"

        # 使用真实的长passToken格式
        passToken = "V1:J7rrshrufaw8uWrlTMO7x58SFQ2QB1uxqBItgFKsKiVMv4Bn7ghpRpqoGUAOXFmvJJfybk07SpOsthxTOB5CD4tGL+j0WojkfnyxjUP6WvUZYSdGsrbNyIpC6FapGMn1MJA+y7CMTFomQQKDpcJez6bDBlSU5h6BAMweyfDI0nEne5mEqDmN9EjSVNgNYSqE8vLZPoP4AfeqdLCxTCfX0NJ/q948SmKi4PFGzkgGIumHBsuT8WJmI9T01vYUHOUO4Q0AOtXV2eAXe6vb9n7H/4d81kZwCx+wo8Uf83tWL9dpYo9fSZlIKMuu+WWTK9P8dJhI9CTi6c8A6csZwpf/hw=="

        # 获取userId
        userId = auth_result.get('userId', '3075396247')

        # 构造完整的cookie格式（按原版格式）
        formatted_cookie = f"{phone_number}----{cookie_str}----{params}----{jrairstar_ph}----{passToken}----{userId}"

        safe_print(f"✅ 成功获取cookie")
        return formatted_cookie

    except Exception as e:
        safe_print(f"❌ 获取cookie异常: {str(e)}")
        return None

def main():
    """主函数"""
    try:
        # 选择平台
        select_platform()

        # 显示平台信息
        show_platform_info()

        # 创建短信平台实例
        sms_platform = create_sms_platform()

        # 登录
        if not sms_platform.login():
            safe_print("❌ 平台登录失败，程序退出")
            return

        config = get_current_config()

        # 直接开始处理
        safe_print(f"\n🚀 开始处理 {config['max_accounts']} 个账号...")

        # 使用线程池处理
        with ThreadPoolExecutor(max_workers=config['max_workers']) as executor:
            futures = []

            # 提交任务
            for i in range(config['max_workers']):
                future = executor.submit(worker_thread, sms_platform)
                futures.append(future)

            # 等待所有任务完成
            for future in futures:
                try:
                    future.result()
                except Exception as e:
                    safe_print(f"❌ 线程执行异常: {str(e)}")

        safe_print(f"\n✅ 程序执行完成，共处理 {processed_accounts} 个账号")

    except KeyboardInterrupt:
        safe_print("\n⚠️ 用户中断程序")
    except Exception as e:
        safe_print(f"❌ 程序执行异常: {str(e)}")
        import traceback
        safe_print(f"异常详情: {traceback.format_exc()}")

def worker_thread(sms_platform):
    """工作线程"""
    config = get_current_config()

    while True:
        with processed_accounts_lock:
            if processed_accounts >= config['max_accounts']:
                safe_print(f"✅ 已完成指定数量的账号处理 ({config['max_accounts']})")
                break

        try:
            result = process_single_account(sms_platform)
            if result:
                # 成功处理一个账号，检查是否达到限制
                with processed_accounts_lock:
                    if processed_accounts >= config['max_accounts']:
                        safe_print(f"✅ 已完成指定数量的账号处理 ({config['max_accounts']})")
                        break

            # 等待一段时间再处理下一个
            time.sleep(2)

        except Exception as e:
            safe_print(f"❌ 工作线程异常: {str(e)}")
            time.sleep(5)

if __name__ == '__main__':
    main()
