#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试整合版功能
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_platform_selection():
    """测试平台选择功能"""
    print("=== 测试平台选择功能 ===")
    
    try:
        # 导入整合版模块
        from 小米跑号整合版 import (
            CURRENT_PLATFORM, 
            get_current_config, 
            create_sms_platform,
            HAOZHU_CONFIG,
            YEZICLOUD_CONFIG
        )
        
        print("✅ 成功导入整合版模块")
        print(f"当前平台: {CURRENT_PLATFORM}")
        
        # 测试配置获取
        config = get_current_config()
        print(f"当前配置: {config['project_id']}")
        
        # 测试平台创建
        sms_platform = create_sms_platform()
        print(f"✅ 成功创建平台实例: {type(sms_platform).__name__}")
        
        print("\n=== 平台配置对比 ===")
        print("豪猪配置:")
        print(f"  - 项目ID: {HAOZHU_CONFIG['project_id']}")
        print(f"  - 最大线程数: {HAOZHU_CONFIG['max_workers']}")
        print(f"  - 最大账号数: {HAOZHU_CONFIG['max_accounts']}")
        print(f"  - 服务器: {HAOZHU_CONFIG['base_url']}")
        
        print("\n椰子云配置:")
        print(f"  - 项目ID: {YEZICLOUD_CONFIG['project_id']}")
        print(f"  - 最大线程数: {YEZICLOUD_CONFIG['max_workers']}")
        print(f"  - 最大账号数: {YEZICLOUD_CONFIG['max_accounts']}")
        print(f"  - 主服务器: {YEZICLOUD_CONFIG['base_url']}")
        print(f"  - 备用服务器: {YEZICLOUD_CONFIG.get('backup_url', 'N/A')}")
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {str(e)}")
        print("请确保在正确的目录中运行此脚本")
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        print(f"异常详情: {traceback.format_exc()}")

def test_api_classes():
    """测试API类功能"""
    print("\n=== 测试API类功能 ===")
    
    try:
        from 小米跑号整合版 import HaoZhuAPI, YeziCloudAPI
        
        print("测试豪猪API类:")
        haozhu_api = HaoZhuAPI()
        print(f"  - 基础URL: {haozhu_api.base_url}")
        print(f"  - 配置: {haozhu_api.config['project_id']}")
        
        print("\n测试椰子云API类:")
        yezi_api = YeziCloudAPI()
        print(f"  - 基础URL: {yezi_api.base_url}")
        print(f"  - 备用URL: {yezi_api.backup_url}")
        print(f"  - 配置: {yezi_api.config['project_id']}")
        
        print("\n✅ API类创建成功")
        
    except Exception as e:
        print(f"❌ 测试API类失败: {str(e)}")

def test_xiaomi_functions():
    """测试小米相关功能"""
    print("\n=== 测试小米相关功能 ===")
    
    try:
        from 小米跑号整合版 import (
            generate_device_id,
            md5_encrypt,
            parse_login_response,
            is_phone_in_whitelist_or_cookies
        )
        
        # 测试设备ID生成
        device_id = generate_device_id()
        print(f"生成的设备ID: {device_id} (长度: {len(device_id)})")
        
        # 测试MD5加密
        test_text = "test123"
        md5_result = md5_encrypt(test_text)
        print(f"MD5加密 '{test_text}': {md5_result}")
        
        # 测试手机号检查
        test_phone = "13800000000"
        is_in_list = is_phone_in_whitelist_or_cookies(test_phone)
        print(f"手机号 {test_phone} 在白名单中: {is_in_list}")
        
        print("✅ 小米相关功能测试完成")
        
    except Exception as e:
        print(f"❌ 测试小米功能失败: {str(e)}")

def show_integration_features():
    """显示整合版特性"""
    print("\n=== 整合版特性 ===")
    
    print("🎯 主要特性:")
    print("1. 双平台支持:")
    print("   - 豪猪云 (haozhu)")
    print("   - 椰子云 (yezicloud)")
    print("")
    print("2. 统一接口:")
    print("   - 相同的API调用方式")
    print("   - 统一的配置管理")
    print("   - 一致的错误处理")
    print("")
    print("3. 灵活配置:")
    print("   - 可以通过修改 CURRENT_PLATFORM 切换平台")
    print("   - 每个平台独立的配置参数")
    print("   - 支持运行时平台选择")
    print("")
    print("4. 增强功能:")
    print("   - OCR重试机制")
    print("   - 验证码错误重试")
    print("   - 手机号验证功能")
    print("   - 自动注册支持")
    
    print("\n🔧 使用方法:")
    print("1. 修改文件顶部的 CURRENT_PLATFORM 变量:")
    print("   CURRENT_PLATFORM = 'yezicloud'  # 使用椰子云")
    print("   CURRENT_PLATFORM = 'haozhu'     # 使用豪猪云")
    print("")
    print("2. 根据需要修改对应平台的配置")
    print("")
    print("3. 运行程序:")
    print("   python 小米跑号整合版.py")
    
    print("\n⚙️ 配置说明:")
    print("- project_id: 项目ID，不同平台使用不同的值")
    print("- max_workers: 最大线程数")
    print("- max_accounts: 最大账号数量限制")
    print("- use_forget_password_check: 是否验证手机号")
    print("- auto_register_if_not_exist: 是否自动注册不存在的号码")
    print("- max_ocr_retries: OCR识别重试次数")
    print("- max_captcha_retries: 验证码重试次数")

def show_platform_comparison():
    """显示平台对比"""
    print("\n=== 平台对比 ===")
    
    print("豪猪云特点:")
    print("✅ 稳定性较好")
    print("✅ API响应快速")
    print("✅ 支持高并发")
    print("⚠️ 需要token认证")
    print("⚠️ 价格相对较高")
    
    print("\n椰子云特点:")
    print("✅ 价格相对便宜")
    print("✅ 支持备用服务器")
    print("✅ 无需token认证")
    print("⚠️ 稳定性一般")
    print("⚠️ 偶尔会有延迟")
    
    print("\n推荐使用场景:")
    print("- 追求稳定性和速度 → 选择豪猪云")
    print("- 追求性价比 → 选择椰子云")
    print("- 需要备用方案 → 两个平台都配置好，随时切换")

if __name__ == '__main__':
    print("小米跑号整合版测试程序")
    print("=" * 60)
    
    choice = input("选择测试模式:\n1. 测试平台选择\n2. 测试API类\n3. 测试小米功能\n4. 显示整合特性\n5. 显示平台对比\n请输入选择 (1-5): ").strip()
    
    if choice == "1":
        test_platform_selection()
    elif choice == "2":
        test_api_classes()
    elif choice == "3":
        test_xiaomi_functions()
    elif choice == "4":
        show_integration_features()
    elif choice == "5":
        show_platform_comparison()
    else:
        print("无效选择，运行所有测试...")
        test_platform_selection()
        test_api_classes()
        test_xiaomi_functions()
        show_integration_features()
