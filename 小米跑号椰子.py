import requests
import os
import json
import uuid
import hashlib
import base64
from urllib.parse import quote
import urllib3
import random
import string
import threading
from concurrent.futures import ThreadPoolExecutor
from queue import Queue
import ddddocr
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import time

# 定义基础目录
BASE_DIR = os.getcwd()

# 线程安全的打印函数
print_lock = threading.Lock()
def safe_print(*args, **kwargs):
    with print_lock:
        print(*args, **kwargs)

# 文件操作锁
file_lock = threading.Lock()

# 椰子云API配置
YEZICLOUD_CONFIG = {
    'username': 'axu8647',  # 椰子云账号
    'password': 'qq47166683',  # 椰子云密码
    'base_url': 'http://api.sqhyw.net:90',
    'backup_url': 'http://api.nnanx.com:90',
    'project_id': '824598',  # 项目ID
    'max_workers': 3,  # 最大线程数
    'max_accounts': 3,  # 最大账号数量限制
    'use_forget_password_check': True,  # 是否使用忘记密码验证手机号有效性（True=使用，False=跳过直接发送验证码）
    'auto_register_if_not_exist': True,  # 当手机号不存在时自动尝试注册发送验证码（包括20003和70016错误码）
    'max_ocr_retries': 5,  # OCR识别重试次数（确保识别出5位验证码）
    'max_captcha_retries': 20  # 验证码重试次数（包括87001错误重试）
}

# 全局计数器
processed_accounts = 0
processed_accounts_lock = threading.Lock()

# 初始化 ddddocr
ocr = ddddocr.DdddOcr(show_ad=False)

class YeziCloudAPI:
    def __init__(self):
        self.token = None
        self.base_url = YEZICLOUD_CONFIG['base_url']
        self.session = requests.Session()
        
    def login(self):
        """登录椰子云API获取token"""
        try:
            ("\n=== 登录椰子云API ===")
            url = f"{self.base_url}/api/logins"
            params = {
                'username': YEZICLOUD_CONFIG['username'],
                'password': YEZICLOUD_CONFIG['password']
            }
            (f"请求URL: {url}")
            (f"请求参数: {params}")
            
            response = self.session.get(url, params=params)
            (f"响应状态码: {response.status_code}")
            (f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if 'token' in result:
                    self.token = result['token']
                    ("✅ 椰子云API登录成功")
                    return True
            (f"❌ 椰子云API登录失败: {response.text}")
            return False
        except Exception as e:
            (f"❌ 椰子云API登录异常: {str(e)}")
            return False
    
    def get_phone(self):
        """获取手机号"""
        if not self.token:
            if not self.login():
                return None
                
        try:
            ("\n=== 获取手机号 ===")
            url = f"{self.base_url}/api/get_mobile"
            params = {
                'token': self.token,
                'project_id': YEZICLOUD_CONFIG['project_id'],
                'operator': '0'  # 指定实卡
            }
            (f"请求URL: {url}")
            (f"请求参数: {params}")
            
            response = self.session.get(url, params=params)
            (f"响应状态码: {response.status_code}")
            (f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('message') == 'ok' and result.get('mobile'):
                    (f"✅ 成功获取手机号: {result['mobile']}")
                    return result['mobile']
            (f"❌ 获取手机号失败: {response.text}")
            return None
        except Exception as e:
            (f"❌ 获取手机号异常: {str(e)}")
            return None
            
    def get_sms_code(self, phone_number):
        """获取短信验证码"""
        if not self.token:
            if not self.login():
                return None
                
        max_retries = 15  # 最大重试次数
        retry_interval = 3  # 重试间隔（秒）
        
        try:
            (f"\n=== 获取短信验证码 (手机号: {phone_number}) ===")
            for i in range(max_retries):
                (f"\n第 {i+1} 次尝试获取验证码...")
                url = f"{self.base_url}/api/get_message"
                params = {
                    'token': self.token,
                    'project_id': YEZICLOUD_CONFIG['project_id'],
                    'phone_num': phone_number
                }
                (f"请求URL: {url}")
                (f"请求参数: {params}")
                
                response = self.session.get(url, params=params)
                (f"响应状态码: {response.status_code}")
                (f"响应内容: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('message') == 'ok' and result.get('code'):
                        safe_print(f"✅ 成功获取验证码: {result['code']}")
                        return result['code']
                (f"等待 {retry_interval} 秒后重试...")
                time.sleep(retry_interval)
            ("❌ 获取验证码超时")
            return None
        except Exception as e:
            (f"❌ 获取验证码异常: {str(e)}")
            return None
            
    def release_phone(self, phone_number):
        """释放手机号"""
        if not self.token:
            if not self.login():
                return False
                
        try:
            (f"\n=== 释放手机号 {phone_number} ===")
            url = f"{self.base_url}/api/free_mobile"
            params = {
                'token': self.token,
                'project_id': YEZICLOUD_CONFIG['project_id'],
                'phone_num': phone_number
            }
            (f"请求URL: {url}")
            (f"请求参数: {params}")
            
            response = self.session.get(url, params=params)
            (f"响应状态码: {response.status_code}")
            (f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('message') == 'ok':
                    (f"✅ 成功释放手机号: {phone_number}")
                    return True
            (f"❌ 释放手机号失败: {response.text}")
            return False
        except Exception as e:
            (f"❌ 释放手机号异常: {str(e)}")
            return False

def generate_device_id():
    """生成随机deviceId"""
    return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16))

def md5_encrypt(text):
    """MD5加密"""
    return hashlib.md5(text.encode('utf-8')).hexdigest().upper()

def generate_signed_url(response_json):
    """根据API响应生成带签名的URL"""
    # 解析JSON响应
    data = json.loads(response_json.replace('&&&START&&&', ''))
    
    # 提取必要参数
    nonce = data['nonce']
    ssecurity = data['ssecurity']
    location = data['location']
    
    # 生成SHA1签名
    sign_data = f"nonce={nonce}&{ssecurity}"
    sha1_hash = hashlib.sha1(sign_data.encode('utf-8')).digest()
    signature = base64.b64encode(sha1_hash).decode('utf-8')
    url_encoded_sign = quote(signature)
    
    # 构造最终URL
    if '?' in location:
        signed_url = f"{location}&_userIdNeedEncrypt=true&clientSign={url_encoded_sign}"
    else:
        signed_url = f"{location}?_userIdNeedEncrypt=true&clientSign={url_encoded_sign}"
    
    return signed_url

def save_cookie_to_file(file_path, formatted_cookie, ticket_token=None, service_token=None, passport_slh=None, passport_ph=None):
    """保存/更新cookie到文件"""
    phone = formatted_cookie.split('----')[0]
    try:
        print(f"\n=== 开始保存cookie到文件 ===")
        print(f"目标文件: {file_path}")
        print(f"手机号: {phone}")
        
        # 使用文件锁保护读写操作
        with file_lock:
            # 保存原始格式
            existing = {}
            if os.path.exists(file_path):
                print(f"文件已存在，读取现有内容...")
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        if '----' in line:
                            p = line.split('----')[0]
                            existing[p] = line.strip()
            
            existing[phone] = formatted_cookie
            
            print(f"写入原始格式cookie...")
            with open(file_path, 'w', encoding='utf-8') as f:
                for cookie in existing.values():
                    f.write(f"{cookie}\n")

            # 保存包含所有token的新格式
            if ticket_token:
                new_file_path = os.path.join(os.path.dirname(file_path), "小米cookie全部.txt")
                backup_file_path = os.path.join(os.path.dirname(file_path), "小米cookie全部备份.txt")
                print(f"\n保存完整token信息到: {new_file_path}")
                print(f"保存完整token信息到备份文件: {backup_file_path}")
                new_existing = {}
                
                if os.path.exists(new_file_path):
                    print(f"完整token文件已存在，读取现有内容...")
                    with open(new_file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            if '----' in line:
                                p = line.split('----')[0]
                                new_existing[p] = line.strip()
                
                # 构造新格式的cookie字符串
                new_formatted_cookie = f"{formatted_cookie}----{ticket_token}----{service_token}----{passport_slh}----{passport_ph}"
                new_existing[phone] = new_formatted_cookie
                
                print(f"写入完整token信息...")
                with open(new_file_path, 'w', encoding='utf-8') as f:
                    for cookie in new_existing.values():
                        f.write(f"{cookie}\n")
                        
                # 同时写入备份文件
                with open(backup_file_path, 'w', encoding='utf-8') as f:
                    for cookie in new_existing.values():
                        f.write(f"{cookie}\n")
                    
        print(f"✅ 已更新 {phone} 的cookie到所有文件")
    except Exception as e:
        print(f"❌ 保存cookie失败: {str(e)}")
        print(f"错误详情: {str(e)}")
        import traceback
        print(traceback.format_exc())

def get_captcha_code(session, max_ocr_retries=5):
    """获取并识别图形验证码，支持OCR重试"""
    for ocr_retry in range(max_ocr_retries):
        try:
            if ocr_retry == 0:
                print(f"\n=== 获取图形验证码 ===")
            else:
                print(f"\n=== 获取图形验证码 (OCR重试 {ocr_retry + 1}/{max_ocr_retries}) ===")

            url = "https://account.xiaomi.com/pass/getCode"
            params = {
                "icodeType": "login"
            }
            headers = {
                'User-Agent': 'MIX 2S/MIX 2S; MIUI/9.9.3 E/V10 B/D L/zh-CN LO/CN APP/xiaomi.account APPV/135'
            }

            print(f"请求URL: {url}")
            print(f"请求参数: {params}")

            response = session.get(url, params=params, headers=headers, verify=False)
            print(f"响应状态码: {response.status_code}")

            if response.status_code == 200:
                # 获取响应中的cookies，特别是ick
                response_cookies = response.cookies.get_dict()
                print(f"获取到的cookies: {list(response_cookies.keys())}")
                if 'ick' in response_cookies:
                    print(f"✅ 成功获取ick cookie")
                else:
                    print("⚠️ 未找到ick cookie")

                # 将cookies更新到session中
                for name, value in response_cookies.items():
                    session.cookies.set(name, value, domain='account.xiaomi.com', path='/')

                # 使用 ddddocr 识别验证码
                captcha_code = ocr.classification(response.content)
                print(f"OCR识别结果: '{captcha_code}' (长度: {len(captcha_code)})")

                if len(captcha_code) == 5:
                    safe_print(f"✅ 图形验证码识别成功: {captcha_code}")
                    return captcha_code
                else:
                    print(f"❌ 图形验证码长度不正确: {len(captcha_code)}位，应为5位")
                    if ocr_retry < max_ocr_retries - 1:
                        print(f"准备进行第 {ocr_retry + 2} 次OCR重试...")
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    else:
                        print("❌ OCR重试次数已用完")
                        return None
            else:
                print(f"❌ 获取图形验证码失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                if ocr_retry < max_ocr_retries - 1:
                    print(f"准备进行第 {ocr_retry + 2} 次重试...")
                    time.sleep(1)
                    continue
                else:
                    return None
        except Exception as e:
            print(f"❌ 获取图形验证码异常: {str(e)}")
            import traceback
            print(f"异常详情: {traceback.format_exc()}")
            if ocr_retry < max_ocr_retries - 1:
                print(f"准备进行第 {ocr_retry + 2} 次重试...")
                time.sleep(1)
                continue
            else:
                return None

    return None

def send_sms_with_captcha(session, phone_number, captcha_code):
    """使用图形验证码发送短信"""
    try:
        print(f"\n=== 使用图形验证码发送短信 ===")
        print(f"手机号: {phone_number}")
        print(f"验证码: {captcha_code}")

        url = "https://account.xiaomi.com/pass/sendServiceLoginTicket"
        data = {
            '_json': 'true',
            'user': phone_number,
            'captCode': captcha_code,
            'sid': 'passportapi',
            '_locale': 'zh_CN'
        }
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'MIX 2S/MIX 2S; MIUI/9.9.3 E/V10 B/D L/zh-CN LO/CN APP/xiaomi.account APPV/135'
        }

        print(f"请求URL: {url}")
        print(f"请求数据: {data}")

        response = session.post(url, data=data, headers=headers, verify=False)

        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        result = parse_login_response(response)
        print(f"解析结果: {result}")

        if result and result.get('code') == 0:
            safe_print("✅ 使用图形验证码发送短信成功")
            return True, result

        print(f"❌ 使用图形验证码发送短信失败")
        print(f"错误代码: {result.get('code') if result else 'N/A'}")
        print(f"错误描述: {result.get('description') if result else '未知错误'}")
        print(f"完整响应: {result}")

        # 常见错误代码分析
        if result:
            error_code = result.get('code')
            if error_code == 87001:
                print("🔍 错误分析: 验证码错误或已过期")
            elif error_code == 20003:
                print("🔍 错误分析: 账号无效或不受支持")
            elif error_code == 70016:
                print("🔍 错误分析: 验证码发送过于频繁")
            else:
                print(f"🔍 错误分析: 未知错误代码 {error_code}")

        return False, result
    except Exception as e:
        print(f"❌ 使用图形验证码发送短信异常: {str(e)}")
        import traceback
        print(f"异常详情: {traceback.format_exc()}")
        return False, None

def save_new_cookie_format(phone_number, cookies_dict):
    """保存新格式的cookie"""
    try:
        new_file_path = os.path.join(BASE_DIR, "小米新cookie.txt")
        print(f"\n=== 开始保存新格式cookie ===")
        print(f"目标文件: {new_file_path}")
        print(f"手机号: {phone_number}")
        
        # 构造新的cookie字符串
        cookie_parts = {
            'identity_session': cookies_dict.get('identity_session', ''),
            'deviceId': cookies_dict.get('deviceId', ''),
            'pass_ua': cookies_dict.get('pass_ua', ''),
            'captchaToken': cookies_dict.get('captchaToken', ''),
            'uLocale': cookies_dict.get('uLocale', ''),
            'ticketToken': cookies_dict.get('ticketToken', ''),
            'passToken': cookies_dict.get('passToken', ''),
            'cUserId': cookies_dict.get('cUserId', ''),
            'userId': cookies_dict.get('userId', ''),
            'passInfo': cookies_dict.get('passInfo', ''),
            'serviceToken': cookies_dict.get('serviceToken', ''),
            'passport_slh': cookies_dict.get('passport_slh', ''),
            'passport_ph': cookies_dict.get('passport_ph', '')
        }
        
        print("\n获取到的cookie值:")
        for k, v in cookie_parts.items():
            if v:
                print(f"{k}: {v}")
        
        # 构建cookie字符串
        cookie_str = '; '.join([f"{k}={v}" for k, v in cookie_parts.items() if v])
        
        # 使用文件锁保护读写操作
        with file_lock:
            # 保存到文件
            existing = {}
            if os.path.exists(new_file_path):
                print(f"文件已存在，读取现有内容...")
                with open(new_file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        if '----' in line:
                            p = line.split('----')[0]
                            existing[p] = line.strip()
            
            existing[phone_number] = f"{phone_number}----{cookie_str}"
            
            print(f"写入新格式cookie...")
            with open(new_file_path, 'w', encoding='utf-8') as f:
                for cookie in existing.values():
                    f.write(f"{cookie}\n")
                
        print(f"✅ 已保存新格式cookie到文件: {new_file_path}")
    except Exception as e:
        print(f"❌ 保存新格式cookie失败: {str(e)}")
        print(f"错误详情: {str(e)}")
        import traceback
        print(traceback.format_exc())

def check_phone_in_white_list(phone_number):
    """检查手机号是否在白号列表或已存在的cookies文件中"""
    try:
        # 检查白号账号密码文件
        white_list_file = '小米视频cookies.txt'
        if os.path.exists(white_list_file):
            with open(white_list_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip() and phone_number in line.split('----')[0]:
                        print(f"手机号 {phone_number} 在白号账号密码文件中找到")
                        return True
        
        # 检查跑号目录下的小米视频cookies文件
        cookies_file = os.path.join(BASE_DIR, "跑号", "小米视频cookies.txt")
        if os.path.exists(cookies_file):
            with open(cookies_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip() and phone_number in line.split('----')[0]:
                        print(f"手机号 {phone_number} 在跑号目录的cookies文件中找到")
                        return True
        
        return False
    except Exception as e:
        print(f"检查白号/cookies文件时出错: {str(e)}")
        return False

def get_reset_pwd_captcha(max_retries=10):
    """获取忘记密码验证码
    
    返回:
    (captcha_code, cookies) - 识别的验证码和获取验证码时的cookies
    """
    url = "https://account.xiaomi.com/pass/getCode"
    
    headers = {
        "Host": "account.xiaomi.com",
        "Connection": "keep-alive",
        "sec-ch-ua-platform": "Windows",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "no-cors",
        "Sec-Fetch-Dest": "image",
        "Referer": "https://account.xiaomi.com/fe/service/forgetPassword",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9"
    }
    
    # 添加验证码类型参数
    params = {
        "icodeType": "resetpwd",
        "_": str(int(time.time() * 1000))  # 添加时间戳防止缓存
    }
    
    session = requests.Session()
    session.verify = False
    
    for attempt in range(max_retries):
        try:
            print(f"尝试获取resetpwd类型验证码图片，第 {attempt+1} 次...")
            response = session.get(url, headers=headers, params=params)
            
            # 检查响应状态
            if response.status_code != 200:
                print(f"获取验证码失败，HTTP状态码: {response.status_code}")
                time.sleep(0.2)
                continue
            
            # 保存cookie
            cookies = response.cookies.get_dict()
            print(f"获取到的cookies: {cookies}")
            
            # 使用ddddocr识别验证码
            captcha_code = ocr.classification(response.content)
            
            print(f"识别到的验证码: {captcha_code}")
            
            # 检查验证码是否为5位
            if len(captcha_code) == 5:
                print(f"成功获取5位验证码: {captcha_code}")
                return captcha_code, cookies
            else:
                print(f"验证码长度不正确: {len(captcha_code)}位，应为5位，重试...")
                time.sleep(0.2)
                continue
            
        except Exception as e:
            print(f"获取验证码时出错: {str(e)}")
            if attempt < max_retries - 1:
                print("稍后重试...")
                time.sleep(0.2)
    
    print("达到最大重试次数，获取验证码失败")
    return None, None

def check_phone_via_forget_password(phone_number, max_attempts=5):
    """通过忘记密码接口检查手机号是否有效
    
    返回:
    bool - 手机号是否有效
    """
    print(f"\n=== 通过忘记密码接口检查手机号 {phone_number} 是否有效 ===")
    
    for attempt in range(max_attempts):
        # 获取resetpwd类型验证码
        captcha_code, cookies = get_reset_pwd_captcha()
        
        if not captcha_code or not cookies:
            print("无法获取resetpwd类型验证码，重试...")
            time.sleep(1)
            continue
        
        # 使用识别的验证码调用忘记密码接口
        url = "https://account.xiaomi.com/pass/forgetPassword"
        
        headers = {
            "Host": "account.xiaomi.com",
            "Connection": "keep-alive",
            "sec-ch-ua-platform": "Windows",
            "X-Requested-With": "XMLHttpRequest",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "application/json, text/plain, */*",
            "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "sec-ch-ua-mobile": "?0",
            "Origin": "https://account.xiaomi.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://account.xiaomi.com/fe/service/forgetPassword",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }
        
        data = {
            "qs": "%3F",
            "sid": "passport",
            "id": phone_number,
            "passToken": "",
            "passport_ph": "",
            "userType": "PH",
            "icode": captcha_code
        }
        
        session = requests.Session()
        session.verify = False
        
        try:
            response = session.post(url, headers=headers, data=data, cookies=cookies)
            print(f"响应状态码: {response.status_code}")
            
            # 尝试解析JSON响应
            try:
                if response.text.startswith('&&&START&&&'):
                    json_str = response.text.replace('&&&START&&&', '')
                    result = json.loads(json_str)
                else:
                    result = response.json()
                
                print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # 检查是否是验证码错误
                if result.get("result") == "error" and result.get("code") == 87001:
                    print("验证码错误，重试...")
                    continue
                
                # 检查账号是否有效
                if result.get("result") == "error" and result.get("code") == 20003:
                    print(f"⚠️ 账号 {phone_number} 无效或不受支持 - 识别为不存在，可以尝试注册")
                    return False, "not_exist"
                elif result.get("result") == "error" and result.get("code") == 70016:
                    print(f"⚠️ 账号 {phone_number} 不存在，可以尝试注册")
                    return False, "not_exist"
                elif result.get("result") == "ok":
                    print(f"✅ 账号 {phone_number} 有效")
                    return True, "exists"
                
                # 其他未知情况，可能需要重试
                print(f"未知响应状态: {result.get('result')}, 代码: {result.get('code')}")
                
            except Exception as e:
                print(f"解析响应时出错: {str(e)}")
            
        except Exception as e:
            print(f"发送请求时出错: {str(e)}")
        
        time.sleep(1)
    
    # 如果达到最大尝试次数仍未得到明确结果，默认认为手机号有效
    print(f"⚠️ 无法确定账号 {phone_number} 是否有效，默认视为有效")
    return True, "unknown"



def process_single_account(yezicloud):
    """处理单个账号的完整流程"""
    global processed_accounts

    try:
        # 先检查是否已达到限制
        with processed_accounts_lock:
            if processed_accounts >= YEZICLOUD_CONFIG['max_accounts']:
                print(f"已达到最大账号数量限制 ({YEZICLOUD_CONFIG['max_accounts']})")
                return True  # 返回True以停止继续处理

        print(f"\n=== 开始获取账号 ===")

        # 1. 获取手机号
        phone_number = yezicloud.get_phone()
        if not phone_number:
            print("❌ 获取手机号失败，等待5秒后重试...")
            time.sleep(5)
            return False

        # 检查手机号是否在白号列表或已存在的cookies文件中
        if check_phone_in_white_list(phone_number):
            print(f"⚠️ 手机号 {phone_number} 已在白号列表或已存在的cookies文件中，跳过处理...")
            yezicloud.release_phone(phone_number)
            return False  # 跳过的账号不计入处理数量

        # 只有在确定要处理这个账号时才增加计数器
        with processed_accounts_lock:
            if processed_accounts >= YEZICLOUD_CONFIG['max_accounts']:
                print(f"已达到最大账号数量限制 ({YEZICLOUD_CONFIG['max_accounts']})")
                yezicloud.release_phone(phone_number)
                return True

            processed_accounts += 1
            current_count = processed_accounts

        print(f"\n=== 开始处理第 {current_count}/{YEZICLOUD_CONFIG['max_accounts']} 个账号 (手机号: {phone_number}) ===")

        # 根据配置决定是否使用忘记密码接口验证手机号
        if YEZICLOUD_CONFIG.get('use_forget_password_check', False):
            print("🔍 启用忘记密码验证模式，正在验证手机号有效性...")
            is_valid, status = check_phone_via_forget_password(phone_number)

            if not is_valid:
                if status == "not_exist" and YEZICLOUD_CONFIG.get('auto_register_if_not_exist', False):
                    print(f"📝 手机号 {phone_number} 不存在，将在后续发送验证码时自动注册...")
                    # 不存在的手机号可以继续处理，小米会自动创建账号
                else:
                    print(f"⚠️ 手机号 {phone_number} 状态: {status}，释放后重试...")
                    yezicloud.release_phone(phone_number)
                    # 手机号无效，回退计数器
                    with processed_accounts_lock:
                        processed_accounts -= 1
                        print(f"手机号无效，已回退计数器，当前已处理: {processed_accounts}")
                    return False
            else:
                print(f"✅ 手机号 {phone_number} 验证通过，状态: {status}")
        else:
            print("⚡ 跳过忘记密码验证，直接发送验证码模式")
        
        # 2. 发送验证码
        session = requests.Session()
        session.verify = False
        base_url = "https://account.xiaomi.com"
        
        def update_session_device_id():
            """更新session的deviceId"""
            device_id = generate_device_id()
            (f"生成新的 deviceId: {device_id}")
            session.cookies.update({
                'sdkVersion': 'accountsdk-18.11.26',
                'fidNonce': 'eyJ0cCI6Im4iLCJub25jZSI6ImpHN1FqWElTbURJQnZKUjMiLCJ2IjoiYWNjb3VudHNkay0xOC4xMS4yNiJ9',
                'deviceId': device_id,
                'fidNonceSign': 'MEYCIQC7LWiC4lI9mTeZOCNE9up40D_9ka980IJdU5uYhA8kcQIhAKyoGF5ASzPwwLQPkPIAfSilcCZuKZNO5gqmu4CkbhPF'
            })
            return device_id
        
        # 首先尝试直接发送验证码
        device_id = update_session_device_id()  # 生成新的deviceId
        
        send_sms_url = f"{base_url}/pass/sendServiceLoginTicket"
        send_sms_data = {
            '_json': 'true',
            'user': phone_number,
            'sid': 'passportapi',
            '_locale': 'zh_CN'
        }
        
        send_response = session.post(
            send_sms_url,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'MIX 2S/MIX 2S; MIUI/9.9.3 E/V10 B/D L/zh-CN LO/CN APP/xiaomi.account APPV/135'
            },
            data=send_sms_data,
            timeout=10
        )
        
        # 解析发送验证码响应
        send_result = parse_login_response(send_response)
        if not send_result:
            print("❌ 发送验证码失败，释放号码后重试...")
            yezicloud.release_phone(phone_number)
            # 发送验证码失败，回退计数器
            with processed_accounts_lock:
                processed_accounts -= 1
                print(f"发送验证码失败，已回退计数器，当前已处理: {processed_accounts}")
            time.sleep(3)
            return False
            
        # 检查是否需要图形验证码
        if send_result.get('code') == 87001:  # 需要图形验证码的错误码
            print("需要图形验证码，开始尝试...")
            max_captcha_retries = YEZICLOUD_CONFIG.get('max_captcha_retries', 20)  # 最大重试次数
            captcha_success = False
            
            for retry_count in range(max_captcha_retries):
                # 每次重试都生成新的deviceId
                device_id = update_session_device_id()
                print(f"\n=== 图形验证码重试 {retry_count + 1}/{max_captcha_retries} ===")
                print(f"当前设备ID: {device_id}")

                captcha_code = get_captcha_code(session, max_ocr_retries=YEZICLOUD_CONFIG.get('max_ocr_retries', 5))
                if not captcha_code:
                    print(f"❌ 第 {retry_count + 1} 次获取验证码失败")
                    if retry_count < max_captcha_retries - 1:
                        print(f"准备重试第 {retry_count + 2} 次...")
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    else:
                        print("❌ 图形验证码重试次数已用完，释放号码后重试...")
                        yezicloud.release_phone(phone_number)
                        # 图形验证码失败，回退计数器
                        with processed_accounts_lock:
                            processed_accounts -= 1
                            print(f"图形验证码失败，已回退计数器，当前已处理: {processed_accounts}")
                        time.sleep(3)
                        return False

                print(f"🔄 第 {retry_count + 1} 次尝试使用验证码 '{captcha_code}' 发送短信...")
                success, sms_result = send_sms_with_captcha(session, phone_number, captcha_code)

                if success:
                    print("✅ 使用图形验证码发送成功")
                    captcha_success = True
                    break
                else:
                    print(f"❌ 第 {retry_count + 1} 次使用验证码发送短信失败")

                    # 检查是否是验证码错误
                    if sms_result and sms_result.get('code') == 87001:
                        print("🔄 验证码错误，将重新获取新的验证码...")
                        # 验证码错误，继续循环会重新获取验证码
                    else:
                        # 其他错误，可能不是验证码问题
                        error_code = sms_result.get('code') if sms_result else 'N/A'
                        print(f"⚠️ 非验证码错误 (错误代码: {error_code})，继续重试...")

                    if retry_count < max_captcha_retries - 1:
                        print(f"准备重试第 {retry_count + 2} 次...")
                        time.sleep(1)  # 等待1秒后重试
                    else:
                        print("❌ 图形验证码重试次数已用完，释放号码后重试...")
                        yezicloud.release_phone(phone_number)
                        # 图形验证码重试用完，回退计数器
                        with processed_accounts_lock:
                            processed_accounts -= 1
                            print(f"图形验证码重试用完，已回退计数器，当前已处理: {processed_accounts}")
                        time.sleep(3)
                        return False

            if not captcha_success:
                print("❌ 图形验证码验证失败，跳过当前账号")
                yezicloud.release_phone(phone_number)
                # 图形验证码验证失败，回退计数器
                with processed_accounts_lock:
                    processed_accounts -= 1
                    print(f"图形验证码验证失败，已回退计数器，当前已处理: {processed_accounts}")
                return False
        elif send_result.get('code') != 0:  # 其他错误
            print(f"❌ 发送验证码失败: {send_result.get('description')}，释放号码后重试...")
            yezicloud.release_phone(phone_number)
            # 其他发送验证码错误，回退计数器
            with processed_accounts_lock:
                processed_accounts -= 1
                print(f"发送验证码其他错误，已回退计数器，当前已处理: {processed_accounts}")
            time.sleep(3)
            return False
            
        print(f"✅ 验证码发送成功到 {phone_number}，等待3秒后开始获取验证码...")
        time.sleep(3)
        
        # 3. 获取验证码
        sms_code = yezicloud.get_sms_code(phone_number)
        if not sms_code:
            print(f"❌ 获取验证码失败 ({phone_number})，释放号码后重试")
            yezicloud.release_phone(phone_number)
            # 获取验证码失败，回退计数器
            with processed_accounts_lock:
                processed_accounts -= 1
                print(f"获取验证码失败，已回退计数器，当前已处理: {processed_accounts}")
            return False
            
        # 4. 使用手机号和验证码获取cookie
        # 注意：这里需要使用相同的 session 和 device_id
        print(f"\n=== 开始为 {phone_number} 获取cookie ===")
        print(f"使用验证码: {sms_code}")
        print(f"使用设备ID: {device_id}")

        login_data = get_xiaomi_cookie_by_sms(phone_number, sms_code, session, device_id)

        if login_data:
            print("\n=== 开始保存所有cookie ===")
            # 1. 保存原始格式cookie
            cookie_file = os.path.join(BASE_DIR, "小米视频cookies.txt")
            formatted_cookie = login_data['formatted_cookie']
            save_cookie_to_file(
                cookie_file,
                formatted_cookie,
                login_data['tokens']['ticketToken'],
                login_data['tokens']['serviceToken'],
                login_data['tokens']['passport_slh'],
                login_data['tokens']['passport_ph']
            )

            # 2. 保存新格式cookie
            save_new_cookie_format(phone_number, login_data['cookies'])

            print(f"✅ {phone_number} 所有cookie保存完成")
        else:
            print(f"\n❌ {phone_number} 获取cookie失败")
            print("=== 失败详情 ===")
            print(f"手机号: {phone_number}")
            print(f"验证码: {sms_code}")
            print(f"设备ID: {device_id}")
            print("请检查上面的详细响应信息以确定失败原因")
            print("常见失败原因:")
            print("1. 验证码过期或错误")
            print("2. 手机号格式不正确")
            print("3. 网络连接问题")
            print("4. 小米服务器响应异常")
            print("5. 设备ID或session问题")
            print("==================")
            # 获取cookie失败，回退计数器
            with processed_accounts_lock:
                processed_accounts -= 1
                print(f"获取cookie失败，已回退计数器，当前已处理: {processed_accounts}")

        # 释放手机号
        yezicloud.release_phone(phone_number)
        return True
        
    except Exception as e:
        print(f"❌ 处理账号时发生异常: {str(e)}")
        if 'phone_number' in locals():
            yezicloud.release_phone(phone_number)
        # 异常情况下，如果已经增加了计数器，需要回退
        if 'current_count' in locals():
            with processed_accounts_lock:
                processed_accounts -= 1
                print(f"异常处理，已回退计数器，当前已处理: {processed_accounts}")
        return False

def get_xiaomi_cookie_by_sms(phone_number, sms_code, session=None, device_id=None):
    """通过验证码获取小米账号Cookie"""
    ("\n=== 开始获取小米Cookie ===")
    
    # 如果没有提供 session 和 device_id，创建新的
    if session is None:
        session = requests.Session()
        session.verify = False
        device_id = generate_device_id()
        (f"生成新的deviceId: {device_id}")
        
        # 设置必要的Cookie
        session.cookies.update({
            'sdkVersion': 'accountsdk-18.11.26',
            'fidNonce': 'eyJ0cCI6Im4iLCJub25jZSI6ImpHN1FqWElTbURJQnZKUjMiLCJ2IjoiYWNjb3VudHNkay0xOC4xMS4yNiJ9',
            'deviceId': device_id,
            'fidNonceSign': 'MEYCIQC7LWiC4lI9mTeZOCNE9up40D_9ka980IJdU5uYhA8kcQIhAKyoGF5ASzPwwLQPkPIAfSilcCZuKZNO5gqmu4CkbhPF'
        })
    
    base_url = "https://account.xiaomi.com"
    
    # 1. phoneInfo请求
    ("\n1. 开始phoneInfo请求...")
    phone_info_url = f"{base_url}/pass/phoneInfo"
    phone_info_data = {
        'ticket': sms_code,
        '_json': 'true',
        'user': phone_number
    }
    
    phone_info_response = session.post(
        phone_info_url,
        headers={
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'MIX 2S/MIX 2S; MIUI/9.9.3 E/V10 B/D L/zh-CN LO/CN APP/xiaomi.account APPV/135'
        },
        data=phone_info_data,
        timeout=10
    )
    
    (f"phoneInfo响应状态码: {phone_info_response.status_code}")
    (f"phoneInfo响应内容: {phone_info_response.text}")
    
    if phone_info_response.status_code != 200:
        print(f"❌ phoneInfo请求失败，状态码: {phone_info_response.status_code}")
        print(f"响应内容: {phone_info_response.text}")
        return None

    phone_info_result = parse_login_response(phone_info_response)
    if not phone_info_result or phone_info_result.get('code') != 0:
        print(f"❌ phoneInfo请求失败")
        print(f"错误代码: {phone_info_result.get('code') if phone_info_result else 'N/A'}")
        print(f"错误描述: {phone_info_result.get('description') if phone_info_result else '未知错误'}")
        print(f"完整响应: {phone_info_result}")
        return None
        
    # 获取ticketToken
    ticket_token = phone_info_result.get('data', {}).get('ticketToken')
    if not ticket_token:
        print("❌ 未能获取到ticketToken")
        print(f"phoneInfo响应数据: {phone_info_result.get('data', {})}")
        print("可能原因:")
        print("1. 验证码错误或已过期")
        print("2. 手机号格式不正确")
        print("3. 小米服务器响应格式变化")
        return None
        
    # 更新ticketToken到cookies
    session.cookies.update({'ticketToken': ticket_token})
    
    # 2. serviceLoginTicketAuth请求
    ("\n2. 开始serviceLoginTicketAuth请求...")
    auth_url = f"{base_url}/pass/serviceLoginTicketAuth"
    auth_data = {
        'bizDeviceType': '',
        'needTheme': 'false',
        'theme': '',
        'showActiveX': 'false',
        'serviceParam': '{"checkSafePhone":false,"checkSafeAddress":false,"lsrp_score":0.0}',
        'callback': 'https://account.xiaomi.com/sts?sign=ZvAtJIzsDsFe60LdaPa76nNNP58%3D&followup=https%3A%2F%2Faccount.xiaomi.com%2Fpass%2Fauth%2Fsecurity%2Fhome&sid=passport',
        'qs': '%3Fcallback%3Dhttps%253A%252F%252Faccount.xiaomi.com%252Fsts%253Fsign%253DZvAtJIzsDsFe60LdaPa76nNNP58%25253D%2526followup%253Dhttps%25253A%25252F%25252Faccount.xiaomi.com%25252Fpass%25252Fauth%25252Fsecurity%25252Fhome%2526sid%253Dpassport%26sid%3Dpassport%26_group%3DDEFAULT',
        'sid': 'passport',
        '_sign': '2&V1_passport&q1Cy9JQ65bbDksgF65wKE1Lz/LI=',
        'user': phone_number,
        'ticket': sms_code,
        '_json': 'true',
        '_locale': 'zh_CN'
    }
    
    auth_response = session.post(
        auth_url,
        headers={
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'application/json, text/plain, */*',
            'X-Requested-With': 'XMLHttpRequest'
        },
        data=auth_data,
        timeout=10
    )
    
    (f"\n=== serviceLoginTicketAuth响应详细信息 ===")
    (f"响应状态码: {auth_response.status_code}")
    (f"响应头: {dict(auth_response.headers)}")
    (f"响应内容: {auth_response.text}")
    ("========================\n")
    
    if auth_response.status_code != 200:
        print(f"❌ serviceLoginTicketAuth请求失败，状态码: {auth_response.status_code}")
        print(f"响应内容: {auth_response.text}")
        return None

    auth_result = parse_login_response(auth_response)
    if not auth_result or auth_result.get('code') != 0:
        print(f"❌ serviceLoginTicketAuth失败")
        print(f"错误代码: {auth_result.get('code') if auth_result else 'N/A'}")
        print(f"错误描述: {auth_result.get('description') if auth_result else '未知错误'}")
        print(f"完整响应: {auth_result}")
        return None

    # 3. 开始sts请求
    ("\n3. 开始sts请求...")
    # 从auth_result中获取location URL并解析参数
    location_url = auth_result.get('location', '')
    if not location_url:
        print("❌ 未能从响应中获取location URL")
        print(f"auth_result内容: {auth_result}")
        print("可能原因:")
        print("1. serviceLoginTicketAuth响应格式变化")
        print("2. 前面的验证步骤失败")
        print("3. 小米服务器返回异常")
        return None
    
    # 解析location URL中的参数
    from urllib.parse import urlparse, parse_qs
    parsed_url = urlparse(location_url)
    query_params = parse_qs(parsed_url.query)
    
    sts_url = "https://account.xiaomi.com/sts"
    sts_params = {
        'sign': query_params.get('sign', [''])[0],
        'followup': query_params.get('followup', [''])[0],
        'sid': query_params.get('sid', [''])[0],
        'd': query_params.get('d', [''])[0],
        'ticket': query_params.get('ticket', [''])[0],
        'pwd': query_params.get('pwd', [''])[0],
        'p_ts': query_params.get('p_ts', [''])[0],
        'fid': query_params.get('fid', [''])[0],
        'p_lm': query_params.get('p_lm', [''])[0],
        'auth': query_params.get('auth', [''])[0],
        'm': query_params.get('m', [''])[0],
        '_group': query_params.get('_group', [''])[0],
        'tsl': query_params.get('tsl', [''])[0],
        'p_ca': query_params.get('p_ca', [''])[0],
        'p_ur': query_params.get('p_ur', [''])[0],
        'p_idc': query_params.get('p_idc', [''])[0],
        'nonce': query_params.get('nonce', [''])[0],
        '_ssign': query_params.get('_ssign', [''])[0]
    }
    
    print("\n=== STS请求详情 ===")
    print(f"请求URL: {sts_url}")
    print(f"请求参数: {json.dumps(sts_params, indent=2, ensure_ascii=False)}")
    # 将CaseInsensitiveDict转换为普通dict
    headers_dict = dict(session.headers)
    print(f"请求头: {json.dumps(headers_dict, indent=2, ensure_ascii=False)}")
    
    # 保存原始cookies
    original_cookies = session.cookies.copy()
    
    sts_response = session.get(
        sts_url,
        params=sts_params,
        headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8'
        },
        allow_redirects=False  # 不自动跟随重定向
    )
    
    print("\n=== STS响应详情 ===")
    print(f"响应状态码: {sts_response.status_code}")
    # 将响应头转换为普通dict
    response_headers = dict(sts_response.headers)
    print(f"响应头: {json.dumps(response_headers, indent=2, ensure_ascii=False)}")
    print(f"响应内容: {sts_response.text[:1000]}")  # 只显示前1000个字符
    
    # 获取新的serviceToken和其他cookie
    new_cookies = sts_response.cookies.get_dict()
    service_token = new_cookies.get('serviceToken', '')
    passport_slh = new_cookies.get('passport_slh', '')
    passport_ph = new_cookies.get('passport_ph', '')
    
    print("\n=== 获取到的Cookie值 ===")
    print(f"serviceToken: {service_token}")
    print(f"passport_slh: {passport_slh}")
    print(f"passport_ph: {passport_ph}")
    
    if not passport_slh or not passport_ph:
        print("\n⚠️ 警告: 未能获取到完整的passport cookie")
        print("当前所有cookie:")
        for k, v in new_cookies.items():
            print(f"{k}: {v}")
        print("\n原始cookies:")
        for k, v in original_cookies.items():
            print(f"{k}: {v}")
    
    # 更新session的cookies，保留原始cookies
    session.cookies.update(original_cookies)
    session.cookies.update(new_cookies)
    
    # 4. 获取identity_session
    try:
        print("\n4. 开始获取identity_session...")
        # 第一步: 获取bindSafeAddress
        bind_url = f"{base_url}/pass/auth/security/bindSafeAddress"
        bind_params = {
            'userId': auth_result.get('userId'),
            'user': auth_result.get('userId'),
            '_json': 'true',
            '_dc': str(int(time.time() * 1000)),
            'callbackPath': f'/fe/service/account?_service=bindEmail&_darkMode=&userId={auth_result.get("userId")}&user={auth_result.get("userId")}&_json=true&_dc={str(int(time.time() * 1000))}'
        }
        
        bind_response = session.get(
            bind_url,
            params=bind_params,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                'Accept': 'application/json, text/plain, */*',
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout=10
        )
        
        if bind_response.status_code == 200:
            bind_result = parse_login_response(bind_response)
            if bind_result and bind_result.get('code') == 2:
                # 第二步: 请求authStart
                auth_url = bind_result.get('url')
                if auth_url:
                    auth_response = session.get(
                        auth_url,
                        headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                            'Upgrade-Insecure-Requests': '1'
                        },
                        allow_redirects=False,
                        timeout=10
                    )
                    
                    if auth_response.status_code == 302:
                        # 第三步: 请求list接口获取identity_session
                        list_url = f"{base_url}/identity/list"
                        list_params = {
                            'sid': 'passportsecurity',
                            'supportedMask': '3758096384',
                            'context': auth_url.split('context=')[1].split('&')[0]
                        }
                        
                        list_response = session.get(
                            list_url,
                            params=list_params,
                            headers={
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                                'Accept': 'application/json, text/plain, */*',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            timeout=10
                        )
                        
                        if list_response.status_code == 200:
                            identity_session = list_response.cookies.get('identity_session')
                            if identity_session:
                                session.cookies.set('identity_session', identity_session, domain='account.xiaomi.com', path='/identity')
                                print("✅ 成功获取identity_session")
    except Exception as e:
        print(f"❌ 获取identity_session异常: {str(e)}")
        
    # 5. 开始米家钱包验证流程
    verify_url = "https://account.xiaomi.com/pass/serviceLogin"
    verify_params = {
        '_json': 'true',
        'appName': 'com.mipay.wallet',
        'sid': 'jrairstar',
        '_locale': 'zh_CN'
    }
    
    verify_response = session.get(
        verify_url,
        params=verify_params,
        headers={
            'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
        },
        verify=False,
        timeout=10
    )
    
    if verify_response.status_code == 200:
        verify_result = parse_login_response(verify_response)
        if verify_result and verify_result.get('code') == 0:
            # 生成签名URL并获取金融服务令牌
            response_json = f"&&&START&&&{json.dumps(verify_result)}"
            signed_url = generate_signed_url(response_json)
            
            jr_sts_response = session.get(
                signed_url,
                headers={
                    'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
                },
                verify=False,
                timeout=10
            )
            
            if jr_sts_response.status_code == 200:
                # 获取所有需要的cookie信息
                cookies = session.cookies.get_dict()
                cUserId = cookies.get('cUserId', '')
                serviceToken = cookies.get('serviceToken', '')
                jrairstar_slh = cookies.get('jrairstar_slh', '')
                jrairstar_ph = cookies.get('jrairstar_ph', '')
                identity_session = cookies.get('identity_session', '')
                
                # 构造cookie字符串
                cookie_str = f"cUserId={cUserId}; jrairstar_serviceToken={serviceToken}; jrairstar_slh={jrairstar_slh}; jrairstar_ph={jrairstar_ph}; identity_session={identity_session}"
                
                # 使用固定的session_id
                params = f"musicVersion=4.32.0.2&session_id=70ed5478-fd42-45cf-95f9-ea029d99ca4e1744729304705&jrairstar_ph={str(uuid.uuid4())}"
                
                # 从verify_result中获取passToken
                passToken = verify_result.get('passToken', '')
                if not passToken:
                    print("❌ 未能获取到passToken")
                    print(f"verify_result内容: {verify_result}")
                    print("可能原因:")
                    print("1. 米家钱包验证响应格式变化")
                    print("2. 前面的步骤未完全成功")
                    print("3. 小米服务器返回异常")
                    return None
                
                # 格式化cookie字符串
                formatted_cookie = f"{phone_number}----{cookie_str}----{params}----{jrairstar_ph}----{passToken}----{auth_result.get('userId')}"
                
                cookie_file = os.path.join(BASE_DIR, "小米视频cookies.txt")
                # 传入所有token进行保存
                save_cookie_to_file(
                    cookie_file, 
                    formatted_cookie, 
                    ticket_token,
                    service_token,
                    passport_slh,
                    passport_ph
                )
                
                safe_print(f"✅ {phone_number} 操作完成")
                
                return {
                    'formatted_cookie': formatted_cookie,
                    'cookies': cookies,
                    'tokens': {
                        'passToken': auth_result.get('passToken'),
                        'userId': auth_result.get('userId'),
                        'ssecurity': auth_result.get('ssecurity'),
                        'psecurity': auth_result.get('psecurity'),
                        'ticketToken': ticket_token,
                        'serviceToken': service_token,
                        'passport_slh': passport_slh,
                        'passport_ph': passport_ph,
                        'identity_session': identity_session
                    }
                }
    
    print("❌ 米家钱包验证失败")
    print("=== 米家钱包验证失败详情 ===")
    print(f"验证URL: {verify_url}")
    print(f"验证参数: {verify_params}")
    print(f"响应状态码: {verify_response.status_code if 'verify_response' in locals() else 'N/A'}")
    if 'verify_response' in locals():
        print(f"响应内容: {verify_response.text}")
    if 'verify_result' in locals():
        print(f"解析结果: {verify_result}")
    print("可能的失败原因:")
    print("1. 前面的步骤未完全成功，导致session状态不正确")
    print("2. 缺少必要的cookie或token")
    print("3. 小米服务器响应异常")
    print("4. 网络连接问题")
    print("==============================")
    return None

def parse_login_response(response):
    """解析登录响应"""
    try:
        text = response.text.replace('&&&START&&&', '')
        return json.loads(text)
    except Exception as e:
        (f"❌ 解析响应异常: {str(e)}")
    return None

if __name__ == '__main__':
    # 创建线程池
    executor = ThreadPoolExecutor(max_workers=YEZICLOUD_CONFIG['max_workers'])
    futures = []
    
    try:
        while processed_accounts < YEZICLOUD_CONFIG['max_accounts']:  # 添加数量限制判断
            # 移除已完成的任务
            futures = [f for f in futures if not f.done()]
            
            # 如果当前任务数小于最大线程数，添加新任务
            while len(futures) < YEZICLOUD_CONFIG['max_workers']:
                # 检查是否达到限制
                if processed_accounts >= YEZICLOUD_CONFIG['max_accounts']:
                    break
                    
                # 创建新的椰子云实例
                yezicloud = YeziCloudAPI()
                if not yezicloud.login():
                    print("❌ 椰子云API登录失败，等待5秒后重试...")
                    time.sleep(5)
                    continue
                
                # 提交新任务
                future = executor.submit(process_single_account, yezicloud)
                futures.append(future)
                print(f">>> 当前活动线程数: {len(futures)}")
            
            # 等待一段时间再检查是否需要添加新任务
            time.sleep(1)
            
        print(f"\n✅ 已完成指定数量的账号处理 ({YEZICLOUD_CONFIG['max_accounts']})")
        
    except KeyboardInterrupt:
        print("\n检测到中断输入，正在等待当前任务完成...")
        # 等待所有正在执行的任务完成
        for future in futures:
            try:
                future.result(timeout=30)  # 等待最多30秒
            except:
                pass
        print("程序已退出")
    finally:
        # 关闭线程池
        executor.shutdown(wait=False)