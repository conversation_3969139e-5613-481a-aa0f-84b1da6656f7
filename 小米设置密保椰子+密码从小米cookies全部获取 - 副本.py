import requests
import json
import urllib3
import time
import webbrowser
import os
import subprocess
import random
import string
from urllib.parse import urlparse, parse_qs
from concurrent.futures import ThreadPoolExecutor, wait, FIRST_COMPLETED, CancelledError
import threading
import signal
import sys
import ddddocr
from PIL import Image
import io

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 程序配置
CONFIG = {
    'thread_count': 5,  # 线程数
    'release_phone': True,  # 是否释放手机号
}

# 椰子云API配置
YEZICLOUD_CONFIG = {
    'username': 'axu8647',  # 椰子云账号
    'password': 'qq********',  # 椰子云密码
    'base_url': 'http://api.sqhyw.net:90',
    'backup_url': 'http://api.nnanx.com:90',
    'project_id': '10008----W45Q33'  # 项目ID
}

# 全局变量用于控制程序运行状态
running = True
# 记录本次运行中已经失败过的账号
failed_accounts = set()
# 添加全局文件锁
file_lock = threading.Lock()

def signal_handler(signum, frame):
    """处理Ctrl+C信号"""
    global running
    print("\n\n⚠️ 正在强制停止程序...")
    running = False
    # 强制退出程序，不等待任何清理
    os._exit(0)

def generate_random_password(length=12):
    """生成随机密码
    包含大小写字母、数字和特殊字符
    """
    # 定义字符集
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = '!@#$%^&*()_+-=[]{}|;:,.<>?'
    
    # 确保每种字符至少出现一次
    password = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special_chars)
    ]
    
    # 填充剩余长度
    remaining_length = length - len(password)
    all_chars = lowercase + uppercase + digits + special_chars
    password.extend(random.choice(all_chars) for _ in range(remaining_length))
    
    # 打乱密码顺序
    random.shuffle(password)
    
    return ''.join(password)

class YeziCloudAPI:
    def __init__(self):
        self.token = None
        self.base_url = YEZICLOUD_CONFIG['base_url']
        self.session = requests.Session()
        
    def login(self):
        """登录椰子云API获取token"""
        try:
            print("\n=== 登录椰子云API ===")
            url = f"{self.base_url}/api/logins"
            params = {
                'username': YEZICLOUD_CONFIG['username'],
                'password': YEZICLOUD_CONFIG['password']
            }
            
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                result = response.json()
                if 'token' in result:
                    self.token = result['token']
                    print("✅ 椰子云API登录成功")
                    return True
            print(f"❌ 椰子云API登录失败: {response.text}")
            return False
        except Exception as e:
            print(f"❌ 椰子云API登录异常: {str(e)}")
            return False

    def release_phone(self, phone_number):
        """释放手机号"""
        if not self.token:
            if not self.login():
                return False
                
        try:
            print(f"\n=== 释放手机号: {phone_number} ===")
            url = f"{self.base_url}/api/free_mobile"
            params = {
                'token': self.token,
                'project_id': YEZICLOUD_CONFIG['project_id'],
                'phone_num': phone_number
            }
            
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('message') == 'ok':
                    print(f"✅ 成功释放手机号: {phone_number}")
                    return True
            print(f"❌ 释放手机号失败: {response.text}")
            return False
        except Exception as e:
            print(f"❌ 释放手机号异常: {str(e)}")
            return False
            
    def get_phone(self):
        """获取手机号"""
        if not self.token:
            if not self.login():
                return None
                
        try:
            print("\n=== 获取手机号 ===")
            url = f"{self.base_url}/api/get_phone"
            params = {
                'token': self.token,
                'project_id': YEZICLOUD_CONFIG['project_id'],
                'phone_num': '',  # 不指定手机号，随机获取
                'scope': '0'  # 全国号码
            }
            
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('message') == 'ok' and result.get('phone'):
                    phone = result['phone']
                    print(f"✅ 成功获取手机号: {phone}")
                    return phone
            print(f"❌ 获取手机号失败: {response.text}")
            return None
        except Exception as e:
            print(f"❌ 获取手机号异常: {str(e)}")
            return None
            
    def get_mobile(self, phone_number):
        """获取指定手机号"""
        if not self.token:
            if not self.login():
                return False
                
        try:
            print(f"\n=== 获取指定手机号: {phone_number} ===")
            url = f"{self.base_url}/api/get_mobile"
            params = {
                'token': self.token,
                'project_id': YEZICLOUD_CONFIG['project_id'],
                'phone_num': phone_number
            }
            
            ("\n=== 取卡请求详情 ===")
            (f"请求URL: {url}")
            (f"请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
            
            response = self.session.get(url, params=params)
            
            (f"\n=== 取卡响应详情 ===")
            (f"状态码: {response.status_code}")
            (f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('message') == 'ok':
                    print(f"✅ 成功获取手机号: {phone_number}")
                    return True
            print(f"❌ 获取手机号失败: {response.text}")
            return False
        except Exception as e:
            print(f"❌ 获取手机号异常: {str(e)}")
            return False
            
    def get_sms_code(self, phone_number):
        """获取短信验证码"""
        if not self.token:
            if not self.login():
                return None
                
        # 检查手机号是否已处理
        processed_accounts = get_processed_accounts()
        if phone_number in processed_accounts:
            print(f"⏭️ 手机号 {phone_number} 已处理过，跳过")
            return None
            
        # 注意：手机号已在外部获取，这里不再进行获取操作
        
        max_retries = 10  # 最大重试次数
        retry_interval = 3  # 重试间隔（秒）
        
        try:
            print(f"\n=== 获取短信验证码 (手机号: {phone_number}) ===")
            url = f"{self.base_url}/api/get_message"
            params = {
                'token': self.token,
                'project_id': YEZICLOUD_CONFIG['project_id'],
                'phone_num': phone_number
            }
            
            for i in range(max_retries):
                if not running:  # 检查程序是否应该继续运行
                    print("\n⚠️ 检测到停止信号，立即停止获取验证码")
                    os._exit(0)  # 直接退出
                    
                print(f"\n第 {i+1} 次尝试获取验证码...")
                try:
                    response = self.session.get(url, params=params, timeout=5)  # 添加超时设置
                except requests.exceptions.Timeout:
                    print("❌ 请求超时，继续尝试...")
                    continue
                except requests.exceptions.RequestException:
                    print("❌ 请求异常，继续尝试...")
                    continue
                    
                if response.status_code == 200:
                    result = response.json()
                    if result.get('message') == 'ok' and result.get('code'):
                        print(f"✅ 成功获取验证码: {result['code']}")
                        return result['code']
                    elif result.get('message') == 'ok' and not result.get('code'):
                        print("⏳ 验证码尚未到达，继续等待...")
                    else:
                        print(f"❌ 获取验证码失败: {result}")
                        # 如果是手机卡不在线等错误，直接返回None
                        if "该手机卡不在线" in str(result):
                            print("❌ 手机卡不在线，跳过此账号")
                            return None
                else:
                    print(f"❌ 请求失败，状态码: {response.status_code}")
                    
                print(f"等待 {retry_interval} 秒后重试...")
                # 分段等待，以便及时响应停止信号
                for _ in range(retry_interval):
                    if not running:
                        print("\n⚠️ 检测到停止信号，立即停止获取验证码")
                        os._exit(0)  # 直接退出
                    time.sleep(1)
                
            print("❌ 获取验证码超时")
            return None
        except Exception as e:
            print(f"❌ 获取验证码异常: {str(e)}")
            return None

def verify_phone_and_set_security(session, phone_number, sms_code, cookies_dict):
    """验证手机号并设置安全信息"""
    try:
        print("\n=== 开始验证手机号 ===")
        # 1. 提交验证码
        verify_url = "https://account.xiaomi.com/identity/auth/verifyPhone"
        verify_data = {
            '_flag': '4',
            'ticket': sms_code,
            'trust': 'false',
            '_json': 'true'
        }
        
        # 检查验证码长度
        if len(sms_code) != 6:
            print(f"❌ 验证码长度不是6位，当前长度: {len(sms_code)}")
            return False
            
        verify_response = session.post(
            verify_url,
            data=verify_data,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': 'https://account.xiaomi.com/fe/service/identity/verifyPhone'
            }
        )
        
        if verify_response.status_code != 200:
            print(f"❌ 验证码提交失败: {verify_response.status_code}")
            return False
            
        verify_result = json.loads(verify_response.text.replace('&&&START&&&', ''))
        if verify_result.get('code') != 0:
            print(f"❌ 验证码验证失败: {verify_result}")
            return False
            
        # 获取location并访问
        location = verify_result.get('location')
        if not location:
            print("❌ 未获取到location")
            return False
            
        print("✅ 验证码验证成功，正在处理后续请求...")
        
        # 2. 访问location获取新的cookie
        location_response = session.get(location, allow_redirects=True)
        if location_response.status_code != 200:
            print(f"❌ 访问location失败: {location_response.status_code}")
            return False
            
        # 获取新的passportsecurity_ph
        new_cookies = session.cookies.get_dict()
        passportsecurity_ph = new_cookies.get('passportsecurity_ph')
        if not passportsecurity_ph:
            print("❌ 未获取到新的passportsecurity_ph")
            return False
            
        print("✅ 成功获取新的安全参数")
        
        # 3. 设置密保问题
        security_url = "https://account.xiaomi.com/pass/auth/security/setSafeQuestions"
        security_data = {
            'userId': cookies_dict['userId'],
            'questions': json.dumps([
                {"q": "您外婆的姓名", "a": "外婆********"},
                {"q": "您外公的姓名", "a": "外公********"},
                {"q": "您高中三年级班主任的名字", "a": "班主任********"},
                {"q": "您小学六年级班主任的名字", "a": "六年级班主任********"}
            ]),
            'verifiedQuestions': json.dumps([
                {"q": "您外婆的姓名", "a": "外婆********"},
                {"q": "您外公的姓名", "a": "外公********"},
                {"q": "您高中三年级班主任的名字", "a": "班主任********"},
                {"q": "您小学六年级班主任的名字", "a": "六年级班主任********"}
            ]),
            'passport_ph': cookies_dict.get('passport_ph', ''),
            'passportsecurity_ph': passportsecurity_ph,
            'passToken': cookies_dict.get('passToken', '')
        }
        
        security_response = session.post(
            security_url,
            data=security_data,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'X-Requested-With': 'XMLHttpRequest'
            }
        )
        
        if security_response.status_code != 200:
            print(f"❌ 设置密保问题失败: {security_response.status_code}")
            return False
            
        security_result = json.loads(security_response.text.replace('&&&START&&&', ''))
        if security_result.get('code') != 0:
            print(f"❌ 设置密保问题失败: {security_result}")
            return False
            
        print("✅ 密保问题设置成功")
        
        # 4. 修改密码
        password_url = "https://account.xiaomi.com/pass/auth/security/changePassword"
        # 生成随机密码
        new_password = generate_random_password()
        print(f"\n=== 生成的随机密码 ===")
        print(f"新密码: {new_password}")
        
        password_data = {
            'userId': cookies_dict['userId'],
            'password': new_password,
            'passport_ph': cookies_dict.get('passport_ph', ''),
            'passportsecurity_ph': passportsecurity_ph,
            'passToken': cookies_dict.get('passToken', '')
        }
        
        password_response = session.post(
            password_url,
            data=password_data,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'X-Requested-With': 'XMLHttpRequest'
            }
        )
        
        if password_response.status_code != 200:
            print(f"❌ 修改密码失败: {password_response.status_code}")
            return False
            
        password_result = json.loads(password_response.text.replace('&&&START&&&', ''))
        # 修改判断逻辑：当返回码为1200103时也判定为成功
        if password_result.get('code') == 0 or password_result.get('code') == 1200103:
            print("✅ 密码修改成功（新密码不能与旧密码相同）")
        else:
            print(f"❌ 修改密码失败: {password_result}")
            return False
            
        # 5. 保存所有更新后的参数
        final_cookies = session.cookies.get_dict()
        save_data = {
            'phone': phone_number,
            'password': new_password,
            'cookies': final_cookies,
            'params': cookies_dict.get('params', ''),
            'jrairstar_ph': cookies_dict.get('jrairstar_ph', ''),
            'passToken': final_cookies.get('passToken', ''),
            'userId': final_cookies.get('userId', ''),
            'serviceToken': final_cookies.get('serviceToken', ''),
            'passport_slh': final_cookies.get('passport_slh', ''),
            'passport_ph': final_cookies.get('passport_ph', ''),
            'passportsecurity_ph': final_cookies.get('passportsecurity_ph', '')
        }
        
        # 保存到白号全部参数.txt
        with open('白号全部参数.txt', 'a', encoding='utf-8') as f:
            f.write(f"{phone_number}----{json.dumps(final_cookies)}----{save_data['params']}----{save_data['jrairstar_ph']}----{save_data['passToken']}----{save_data['userId']}----{save_data['serviceToken']}----{save_data['passport_slh']}----{save_data['passport_ph']}----{save_data['passportsecurity_ph']}\n")
            
        # 保存到白号账号密码.txt
        with open('白号账号密码.txt', 'a', encoding='utf-8') as f:
            f.write(f"{phone_number}----{new_password}\n")
            
        print("✅ 所有参数已保存到文件")
        return True
        
    except Exception as e:
        print(f"❌ 处理过程发生异常: {str(e)}")
        return False

def open_chrome_incognito(url):
    """使用谷歌浏览器无痕模式打开URL，每个URL打开一个独立窗口"""
    try:
        # 获取Chrome浏览器路径
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser("~") + r"\AppData\Local\Google\Chrome\Application\chrome.exe"
        ]
        
        chrome_path = None
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_path = path
                break
                
        if not chrome_path:
            print("❌ 未找到Chrome浏览器，尝试使用默认浏览器")
            webbrowser.open(url)
            return
            
        # 生成唯一的用户数据目录
        user_data_dir = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Temp", f"chrome_profile_{int(time.time())}_{random.randint(1000, 9999)}")
        
        # 使用无痕模式打开URL，添加--new-window参数确保打开新窗口
        # 使用--user-data-dir参数创建独立的用户数据目录
        subprocess.Popen([
            chrome_path,
            "--incognito",
            "--new-window",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            url
        ])
        print("✅ 已在新的Chrome无痕窗口中打开URL")
    except Exception as e:
        print(f"❌ 打开Chrome无痕模式失败: {str(e)}")
        print("尝试使用默认浏览器打开...")
        webbrowser.open(url)

def get_processed_accounts():
    """获取已处理过的账号列表"""
    global file_lock
    processed_accounts = set()
    try:
        with file_lock:
            if os.path.exists('白号账号密码.txt'):
                with open('白号账号密码.txt', 'r', encoding='utf-8') as f:
                    for line in f:
                        if '----' in line:
                            phone = line.split('----')[0]
                            processed_accounts.add(phone)
    except Exception as e:
        print(f"❌ 读取已处理账号文件失败: {str(e)}")
    return processed_accounts

def get_captcha(session, max_retries=5):
    """获取并识别验证码"""
    url = "https://account.xiaomi.com/pass/getCode"
    
    headers = {
        "Host": "account.xiaomi.com",
        "Connection": "keep-alive",
        "sec-ch-ua-platform": "Windows",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "no-cors",
        "Sec-Fetch-Dest": "image",
        "Referer": "https://account.xiaomi.com/fe/service/identity/verifyPhone",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cookie": "uLocale=zh_CN"
    }
    
    params = {
        "icodeType": "login",
        "t": str(time.time())
    }
    
    retry_count = 0
    while retry_count < max_retries:
        try:
            response = session.get(url, headers=headers, params=params)
            if response.status_code == 200:
                # 获取cookie
                cookies = response.cookies
                cookie_dict = {cookie.name: cookie.value for cookie in cookies}
                
                # 使用ddddocr识别验证码
                ocr = ddddocr.DdddOcr()
                result = ocr.classification(response.content)
                
                # 验证码长度检查
                if len(result) == 5:
                    # 保存验证码图片（可选）
                    img = Image.open(io.BytesIO(response.content))
                    img.save("captcha.png")
                    
                    return {
                        "captcha_code": result,
                        "cookies": cookie_dict
                    }
                else:
                    print(f"验证码长度不正确（当前长度：{len(result)}），正在重试...")
                    retry_count += 1
                    time.sleep(1)  # 添加延迟，避免请求过快
                    continue
            else:
                print(f"获取验证码失败，状态码：{response.status_code}")
                return None
        except Exception as e:
            print(f"发生错误：{str(e)}")
            retry_count += 1
            time.sleep(1)
            continue
    
    print(f"已达到最大重试次数（{max_retries}次），未能获取到5位验证码")
    return None

def send_verification_code(session, captcha_code, cookies, auth_url, max_retries=3):
    """发送验证码"""
    url_send = "https://account.xiaomi.com/identity/auth/sendPhoneTicket"
    
    # 重试计数器
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 获取新的identity_session
            (f"\n=== 开始获取新的identity_session (尝试 {retry_count + 1}/{max_retries}) ===")
            
            ("1. 发送authStart请求...")
            auth_response = session.get(
                auth_url,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                    'Upgrade-Insecure-Requests': '1'
                },
                allow_redirects=False,
                timeout=10
            )
            (f"authStart响应状态码：{auth_response.status_code}")
            (f"authStart响应头：{dict(auth_response.headers)}")
            
            if auth_response.status_code == 302:
                # 从重定向URL中提取context参数
                context = auth_url.split('context=')[1].split('&')[0]
                (f"\n2. 提取context参数：{context}")
                
                # 请求list接口
                ("\n3. 发送list请求...")
                list_url = "https://account.xiaomi.com/identity/list"
                list_params = {
                    'sid': 'passportsecurity',
                    'context': context
                }
                
                list_response = session.get(
                    list_url,
                    params=list_params,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Accept': 'application/json, text/plain, */*',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    timeout=10
                )
                (f"list响应状态码：{list_response.status_code}")
                (f"list响应头：{dict(list_response.headers)}")
                
                if list_response.status_code == 200:
                    identity_session = list_response.cookies.get('identity_session')
                    if identity_session:
                        (f"\n✅ 成功获取identity_session：{identity_session}")
                        # 设置cookie
                        session.cookies.set('identity_session', identity_session, domain='account.xiaomi.com', path='/identity')
                    else:
                        ("\n❌ 未在list响应中找到identity_session")
                        retry_count += 1
                        continue
                else:
                    print(f"\n❌ list请求失败，状态码：{list_response.status_code}")
                    retry_count += 1
                    continue
            else:
                print(f"\n❌ authStart请求未返回302，状态码：{auth_response.status_code}")
                retry_count += 1
                continue
                
            # 如果是重试，重新获取验证码
            if retry_count > 0:
                print("\n=== 重新获取验证码 ===")
                captcha_result = get_captcha(session)
                if not captcha_result:
                    print("❌ 重新获取验证码失败")
                    retry_count += 1
                    continue
                
                print(f"新的验证码识别结果：{captcha_result['captcha_code']}")
                captcha_code = captcha_result['captcha_code']
                cookies = captcha_result['cookies']
            
            # 构建cookie字符串
            cookie_str = f"identity_session={identity_session}; uLocale=zh_CN; ick={cookies.get('ick', '')}"
            
            headers = {
                "Host": "account.xiaomi.com",
                "Connection": "keep-alive",
                "Content-Length": "30",
                "sec-ch-ua-platform": "Windows",
                "X-Requested-With": "XMLHttpRequest",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "application/json, text/plain, */*",
                "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "sec-ch-ua-mobile": "?0",
                "Origin": "https://account.xiaomi.com",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": "https://account.xiaomi.com/fe/service/identity/verifyPhone",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Accept-Language": "zh-CN,zh;q=0.9",
                "Cookie": cookie_str
            }
            
            data = {
                "retry": "0",
                "icode": captcha_code,
                "_json": "true"
            }
            
            ("\n=== 发送验证码请求详情 ===")
            (f"URL: {url_send}")
            (f"Headers: {json.dumps(headers, indent=2, ensure_ascii=False)}")
            (f"Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            response = session.post(url_send, headers=headers, data=data)
            (f"\n=== 验证码发送响应 ===")
            (f"状态码: {response.status_code}")
            (f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                # 处理响应内容，移除可能的&&&START&&&前缀
                response_text = response.text.replace('&&&START&&&', '')
                try:
                    response_json = json.loads(response_text)
                    (f"响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
                    
                    # 检查响应状态
                    if response_json.get('code') == 0:
                        print("✅ 验证码发送成功")
                        return response_text
                    else:
                        error_desc = response_json.get('desc', '未知错误')
                        print(f"❌ 验证码发送失败: {error_desc}")
                        
                        # 如果是验证码错误，则重试
                        if "验证码输入错误" in error_desc:
                            print("⚠️ 验证码识别错误，将重新获取验证码并重试")
                            retry_count += 1
                            continue
                        else:
                            # 其他错误直接返回失败
                            return None
                except:
                    (f"响应内容: {response_text}")
                    # 如果无法解析JSON，也返回响应文本
                    return response.text
            else:
                print(f"❌ 发送验证码失败，状态码：{response.status_code}")
                (f"响应内容: {response.text}")
                retry_count += 1
                continue
                
        except Exception as e:
            print(f"❌ 发送验证码时发生错误：{str(e)}")
            retry_count += 1
            continue
    
    print(f"❌ 已达到最大重试次数（{max_retries}次），验证码发送失败")
    return None

def process_single_account(account_info):
    """处理单个账号的函数"""
    global running, failed_accounts
    try:
        # 检查程序是否应该继续运行
        if not running:
            os._exit(0)  # 直接退出
            
        line = account_info.strip()
        if '----' not in line:
            print("❌ 账号信息格式错误")
            return False, None
            
        parts = line.split('----')
        if len(parts) < 6:
            print("❌ 账号信息不完整")
            return False, None
            
        current_phone = parts[0]
        
        # 检查账号是否已处理
        processed_accounts = get_processed_accounts()
        if current_phone in processed_accounts:
            print(f"⏭️ 账号 {current_phone} 已处理过，跳过")
            return True, current_phone
        
        # 检查该账号是否已经失败过
        if current_phone in failed_accounts:
            print(f"\n⚠️ 账号 {current_phone} 在本次运行中已经失败过一次，程序将停止运行")
            # 停止程序运行
            running = False
            return False, current_phone
        
        cookies_str = parts[1]
        params = parts[2]
        jrairstar_ph = parts[3]
        passToken = parts[4]
        userId = parts[5]
        
        print(f"\n=== 处理账号信息 ===")
        print(f"手机号: {current_phone}")
        print(f"userId: {userId}")
        
        # 从cookie中提取必要的cookie
        cookies_dict = {}
        for cookie in cookies_str.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                cookies_dict[name] = value
        
        # 添加其他必要的cookie
        cookies_dict.update({
            'passToken': passToken,
            'userId': userId,
            'jrairstar_ph': jrairstar_ph
        })
        
        # 创建session并设置cookies
        session = requests.Session()
        session.verify = False
        session.cookies.update(cookies_dict)
        
        base_url = "https://account.xiaomi.com"
        
        # 获取bindSafeAddress
        bind_url = f"{base_url}/pass/auth/security/bindSafeAddress"
        bind_params = {
            'userId': userId,
            'user': userId,
            '_json': 'true',
            '_dc': str(int(time.time() * 1000)),
            'callbackPath': f'/fe/service/account?_service=bindEmail&_darkMode=&userId={userId}&user={userId}&_json=true&_dc={str(int(time.time() * 1000))}'
        }
        
        ("\n=== 发送bindSafeAddress请求 ===")
        (f"URL: {bind_url}")
        (f"参数: {json.dumps(bind_params, indent=2, ensure_ascii=False)}")
        (f"Cookies: {json.dumps(cookies_dict, indent=2, ensure_ascii=False)}")
        
        bind_response = session.get(
            bind_url,
            params=bind_params,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': 'https://account.xiaomi.com/fe/service/account',
                'Origin': 'https://account.xiaomi.com'
            },
            timeout=10
        )
        
        (f"\n=== bindSafeAddress响应 ===")
        (f"状态码: {bind_response.status_code}")
        (f"响应头: {dict(bind_response.headers)}")
        
        if bind_response.status_code == 200:
            bind_result = json.loads(bind_response.text.replace('&&&START&&&', ''))
            (f"响应内容: {json.dumps(bind_result, indent=2, ensure_ascii=False)}")
            
            if bind_result and bind_result.get('code') == 2:
                auth_url = bind_result.get('url')
                if auth_url:
                    (f"\n✅ 成功获取Auth URL: {auth_url}")
                    
                    # 在关键操作前检查running状态
                    if not running:
                        os._exit(0)  # 直接退出
                    
                    # 初始化椰子云API
                    yezicloud = YeziCloudAPI()
                    if not yezicloud.login():
                        print("❌ 椰子云API登录失败")
                        update_accounts_file(current_phone, success=False)
                        return False, current_phone
                    
                    # 先获取手机号
                    print(f"\n=== 开始获取手机号 {current_phone} ===")
                    if not yezicloud.get_mobile(current_phone):
                        print("❌ 获取手机号失败")
                        update_accounts_file(current_phone, success=False)
                        return False, current_phone
                    
                    print(f"✅ 成功获取手机号: {current_phone}")
                    
                    # 验证码获取和发送的重试次数
                    max_captcha_retries = 3
                    captcha_retry_count = 0
                    
                    while captcha_retry_count < max_captcha_retries:
                        print(f"\n=== 验证码处理尝试 {captcha_retry_count + 1}/{max_captcha_retries} ===")
                        
                        # 获取验证码
                        print("\n=== 开始获取验证码 ===")
                        captcha_result = get_captcha(session)
                        if not captcha_result:
                            print("❌ 获取验证码失败")
                            captcha_retry_count += 1
                            if captcha_retry_count >= max_captcha_retries:
                                # 释放手机号
                                if CONFIG['release_phone']:
                                    yezicloud.release_phone(current_phone)
                                update_accounts_file(current_phone, success=False)
                                return False, current_phone
                            print("尝试重新获取验证码...")
                            continue
                        
                        print(f"验证码识别结果：{captcha_result['captcha_code']}")
                        
                        # 发送验证码
                        print("\n=== 发送验证码 ===")
                        send_result = send_verification_code(session, captcha_result['captcha_code'], captcha_result['cookies'], auth_url)
                        if not send_result:
                            print("❌ 发送验证码失败")
                            captcha_retry_count += 1
                            if captcha_retry_count >= max_captcha_retries:
                                # 释放手机号
                                if CONFIG['release_phone']:
                                    yezicloud.release_phone(current_phone)
                                update_accounts_file(current_phone, success=False)
                                return False, current_phone
                            print("尝试重新发送验证码...")
                            continue
                        
                        print("✅ 验证码发送成功")
                        break  # 成功发送验证码，跳出重试循环
                    
                    # 获取短信验证码
                    print("\n=== 开始获取短信验证码 ===")
                    print(f"正在获取手机号 {current_phone} 的验证码...")
                    sms_code = yezicloud.get_sms_code(current_phone)
                    
                    if not running:  # 再次检查running状态
                        os._exit(0)  # 直接退出
                    
                    if not sms_code:
                        print("❌ 获取验证码失败")
                        # 释放手机号
                        if CONFIG['release_phone']:
                            yezicloud.release_phone(current_phone)
                        update_accounts_file(current_phone, success=False)
                        return False, current_phone
                    
                    print(f"\n✅ 成功获取验证码: {sms_code}")
                    
                    # 开始验证流程
                    if verify_phone_and_set_security(session, current_phone, sms_code, cookies_dict):
                        print("\n✅ 账号安全设置完成")
                        # 如果设置了释放手机号，则释放
                        if CONFIG['release_phone']:
                            yezicloud.release_phone(current_phone)
                        # 成功处理，从文件中移除
                        update_accounts_file(current_phone, success=True)
                        return True, current_phone
                    else:
                        print("\n❌ 账号安全设置失败")
                        # 释放手机号
                        if CONFIG['release_phone']:
                            yezicloud.release_phone(current_phone)
                        update_accounts_file(current_phone, success=False)
                        return False, current_phone
                return False, current_phone
            elif bind_result and bind_result.get('code') == 302:
                print(f"❌ 账号需要重新登录，删除账号: {current_phone}")
                update_accounts_file(current_phone, success=False)
                return True, current_phone
            else:
                print(f"❌ bindSafeAddress请求失败: {bind_result.get('description') if bind_result else '未知错误'}")
                update_accounts_file(current_phone, success=False)
                return False, current_phone
        else:
            print(f"❌ bindSafeAddress请求失败，状态码: {bind_response.status_code}")
            update_accounts_file(current_phone, success=False)
            return False, current_phone
            
    except Exception as e:
        print(f"❌ 处理账号异常: {str(e)}")
        update_accounts_file(current_phone, success=False)
        return False, None

def update_accounts_file(phone, success=True):
    """更新账号文件，成功处理的账号移除，失败的移到末尾"""
    global failed_accounts, running, file_lock
    
    with file_lock:
        try:
            with open('小米cookie全部.txt', 'r', encoding='utf-8') as f:
                accounts = f.readlines()
            
            if success:
                # 成功处理的账号直接移除
                remaining_accounts = [acc for acc in accounts if not acc.startswith(f"{phone}----")]
            else:
                # 检查该账号是否已经失败过
                if phone in failed_accounts:
                    print(f"\n⚠️ 账号 {phone} 在本次运行中已经失败过一次，程序将停止运行")
                    # 停止程序运行
                    running = False
                    return
                
                # 记录失败账号
                failed_accounts.add(phone)
                
                # 失败的账号移到末尾
                remaining_accounts = [acc for acc in accounts if not acc.startswith(f"{phone}----")]
                failed_account = next((acc for acc in accounts if acc.startswith(f"{phone}----")), None)
                if failed_account:
                    remaining_accounts.append(failed_account)
            
            # 只有当还有剩余账号时才写入文件
            if remaining_accounts:
                with open('小米cookie全部.txt', 'w', encoding='utf-8') as f:
                    f.writelines(remaining_accounts)
                if success:
                    print(f"\n✅ 已从文件中移除账号: {phone}")
                else:
                    print(f"\n⚠️ 已将失败的账号 {phone} 移到文件末尾")
            else:
                # 如果没有剩余账号，清空文件
                with open('小米cookie全部.txt', 'w', encoding='utf-8') as f:
                    f.write('')
                print("\n✅ 所有账号已处理完成，文件已清空")
        except Exception as e:
            print(f"❌ 更新账号文件失败: {str(e)}")

def main():
    global running, failed_accounts, file_lock
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 获取命令行参数
    phone_number = None
    if len(sys.argv) > 1:
        phone_number = sys.argv[1]
        print(f"\n=== 使用指定的手机号: {phone_number} ===")
    else:
        print("\n=== 处理所有账号 ===")
    
    # 使用配置中的设置
    thread_count = CONFIG['thread_count']
    
    print(f"\n使用 {thread_count} 个线程处理账号")
    
    # 用于跟踪正在处理的账号
    processing_accounts = set()
    
    while running:
        try:
            # 读取所有账号信息，加锁保护
            with file_lock:
                with open('小米cookie全部.txt', 'r', encoding='utf-8') as f:
                    accounts = f.readlines()
                
            if not accounts:
                print("\n=== 所有账号处理完成 ===")
                break
                
            # 如果指定了手机号，只处理该手机号
            if phone_number:
                accounts = [acc for acc in accounts if acc.startswith(f"{phone_number}----")]
                if not accounts:
                    print(f"❌ 未找到手机号 {phone_number} 的账号信息")
                    break
            
            # 获取已处理的账号列表
            processed_accounts = get_processed_accounts()
            
            # 过滤掉已处理和正在处理的账号
            accounts = [acc for acc in accounts 
                       if not acc.split('----')[0] in processed_accounts 
                       and not acc.split('----')[0] in processing_accounts]
            
            # 检查当前待处理的账号中是否有在本次运行中已经失败过的
            for acc in accounts:
                current_phone = acc.split('----')[0]
                if current_phone in failed_accounts:
                    print(f"\n⚠️ 检测到账号 {current_phone} 在本次运行中已经失败过一次，程序将停止运行")
                    running = False
                    break
            
            if not running:
                break
                
            if not accounts:
                print("\n=== 所有账号处理完成 ===")
                break
            
            # 使用线程池处理账号
            with ThreadPoolExecutor(max_workers=thread_count) as executor:
                # 创建任务队列
                futures = set()
                
                # 初始提交任务
                for account in accounts[:thread_count]:
                    if not running:
                        break
                    current_phone = account.split('----')[0]
                    if current_phone not in processing_accounts:
                        processing_accounts.add(current_phone)
                        future = executor.submit(process_single_account, account)
                        futures.add(future)
                
                # 动态处理任务
                while futures and running:
                    try:
                        # 等待任意一个任务完成
                        done, futures = wait(futures, return_when=FIRST_COMPLETED)
                        
                        # 处理完成的任务
                        for future in done:
                            try:
                                success, phone = future.result()
                                if phone:
                                    processing_accounts.remove(phone)  # 从处理中移除
                            except CancelledError:
                                print("\n⚠️ 任务被取消")
                            except Exception as e:
                                print(f"❌ 处理账号时发生错误: {str(e)}")
                        
                        # 如果程序正在运行，提交新任务
                        if running:
                            # 重新读取文件以获取最新状态，加锁保护
                            with file_lock:
                                with open('小米cookie全部.txt', 'r', encoding='utf-8') as f:
                                    current_accounts = f.readlines()
                            
                            # 获取最新的已处理账号列表
                            current_processed = get_processed_accounts()
                            
                            # 过滤掉已处理和正在处理的账号以及本次运行中失败过的账号
                            remaining_accounts = [acc for acc in current_accounts 
                                                if not acc.split('----')[0] in current_processed 
                                                and not acc.split('----')[0] in processing_accounts
                                                and not acc.split('----')[0] in failed_accounts]
                            
                            if remaining_accounts:
                                new_account = remaining_accounts[0]
                                current_phone = new_account.split('----')[0]
                                processing_accounts.add(current_phone)
                                future = executor.submit(process_single_account, new_account)
                                futures.add(future)
                            elif not futures:  # 如果没有剩余账号且没有正在处理的任务，退出循环
                                break
                    except KeyboardInterrupt:
                        print("\n\n⚠️ 正在强制停止程序...")
                        running = False
                        # 取消所有未完成的任务
                        for future in futures:
                            future.cancel()
                        # 强制关闭线程池
                        executor.shutdown(wait=False)
                        break
            
            if not running:
                break
                
            # 检查文件是否为空，加锁保护
            with file_lock:
                with open('小米cookie全部.txt', 'r', encoding='utf-8') as f:
                    if not f.read().strip():
                        print("\n=== 所有账号处理完成 ===")
                        break
            
            # 短暂延迟，避免请求过快
            time.sleep(2)
            
        except FileNotFoundError:
            print("❌ 未找到小米cookie全部.txt文件")
            break
        except Exception as e:
            print(f"\n程序发生异常: {str(e)}")
            break
    
    print("\n程序已停止")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序已强制终止")
        os._exit(0)  # 直接退出
    except Exception as e:
        print(f"\n程序发生异常: {str(e)}")
        os._exit(1)  # 直接退出 