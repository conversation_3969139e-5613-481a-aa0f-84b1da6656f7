#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试图形验证码功能
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入主模块
from 小米账号密码登录获取cookie import get_xiaomi_cookie

def test_captcha_login():
    """测试图形验证码登录"""
    print("=== 测试图形验证码登录功能 ===")

    # 测试账号（请替换为实际的测试账号）
    username = "13026611543"  # 替换为您的测试账号
    password = "your_password"  # 替换为您的测试密码

    print(f"测试账号: {username}")
    print("开始登录测试...")
    print("注意：此测试会自动处理图形验证码")
    print("如果遇到87001错误，程序会自动获取并识别验证码")

    try:
        result = get_xiaomi_cookie(username, password)

        if result:
            print("\n✅ 登录测试成功!")
            print("获取到的数据:")
            if 'formatted_cookie' in result:
                print(f"格式化Cookie: {result['formatted_cookie']}")
            if 'tokens' in result:
                tokens = result['tokens']
                print(f"passToken: {tokens.get('passToken')}")
                print(f"userId: {tokens.get('userId')}")
                print(f"psecurity: {tokens.get('psecurity')}")
        else:
            print("\n❌ 登录测试失败!")
            print("可能的原因:")
            print("1. 账号密码错误")
            print("2. 验证码识别失败")
            print("3. 网络连接问题")
            print("4. 小米服务器限制")

    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        import traceback
        print(f"异常详情: {traceback.format_exc()}")

def test_captcha_only():
    """仅测试验证码获取和识别功能"""
    print("\n=== 测试验证码获取功能 ===")

    import requests
    session = requests.Session()
    session.verify = False

    try:
        from 小米账号密码登录获取cookie import get_captcha_code

        captcha_code, captcha_cookies = get_captcha_code(session)

        if captcha_code:
            print(f"✅ 验证码获取成功: {captcha_code}")
            print(f"✅ 获取到的cookies: {captcha_cookies}")
            if 'ick' in captcha_cookies:
                print(f"✅ ick cookie: {captcha_cookies['ick']}")
            else:
                print("⚠️ 未找到ick cookie")
        else:
            print("❌ 验证码获取失败")

    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        print(f"异常详情: {traceback.format_exc()}")

if __name__ == '__main__':
    print("选择测试模式:")
    print("1. 完整登录测试（包含验证码处理）")
    print("2. 仅测试验证码获取功能")

    choice = input("请输入选择 (1 或 2): ").strip()

    if choice == "1":
        test_captcha_login()
    elif choice == "2":
        test_captcha_only()
    else:
        print("无效选择，执行完整登录测试...")
        test_captcha_login()
