import re

def extract_phone_from_cookie(cookie_line):
    """从cookie行中提取手机号"""
    parts = cookie_line.split('----')
    if len(parts) > 0:
        return parts[0]
    return None

def load_password_dict(password_file):
    """加载密码文件为字典{手机号: 密码}"""
    password_dict = {}
    with open(password_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if '----' in line:
                phone, password = line.split('----', 1)
                password_dict[phone] = password
    return password_dict

def extract_matching_accounts(cookie_file, password_dict, output_file):
    """提取匹配的账号密码"""
    matched_count = 0
    with open(cookie_file, 'r', encoding='utf-8') as f, \
         open(output_file, 'w', encoding='utf-8') as out_f:
        
        for line in f:
            line = line.strip()
            phone = extract_phone_from_cookie(line)
            if phone and phone in password_dict:
                password = password_dict[phone]
                out_f.write(f"{phone}----{password}\n")
                matched_count += 1
    
    return matched_count

def main():
    # 输入文件
    cookie_file = '小米cookie全部.txt'
    password_file = '白号账号密码全部.txt'
    # 输出文件
    output_file = '提取出来的账号密码.txt'
    
    # 加载密码字典
    print("正在加载密码文件...")
    password_dict = load_password_dict(password_file)
    print(f"已加载 {len(password_dict)} 条密码记录")
    
    # 提取匹配的账号
    print("正在提取匹配的账号密码...")
    matched_count = extract_matching_accounts(cookie_file, password_dict, output_file)
    print(f"完成！共提取 {matched_count} 条匹配记录，已保存到 {output_file}")

if __name__ == '__main__':
    main()