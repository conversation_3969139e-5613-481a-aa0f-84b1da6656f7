# 智能判断新老号的修正代码片段

# 替换原来的模式处理逻辑
def smart_account_detection(phone_number):
    """智能判断新老号"""
    safe_print("🧠 智能判断新老号：通过忘记密码接口自动检测...")
    is_valid, status = check_phone_via_forget_password(phone_number, max_attempts=3)
    
    if is_valid and status == "exists":
        safe_print(f"👤 检测到老号: {phone_number} - 使用登录流程")
        account_type = "老号登录"
    elif not is_valid and status in ["not_exist", "invalid"]:
        safe_print(f"📝 检测到新号: {phone_number} - 使用注册流程")
        account_type = "新号注册"
    else:
        safe_print(f"⚠️ 无法确定 {phone_number} 状态，默认使用登录流程")
        account_type = "默认登录"
    
    safe_print(f"📋 账号类型: {account_type}")
    return account_type

# 在主处理函数中的替换代码：
# 将第882-891行替换为：
"""
        # 3. 智能判断新老号
        account_type = smart_account_detection(phone_number)
"""
