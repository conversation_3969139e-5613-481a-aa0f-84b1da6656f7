# 小米账号密码登录 - 图形验证码功能说明

## 功能概述

已成功为 `小米账号密码登录获取cookie.py` 添加了图形验证码处理功能，当小米登录接口返回错误代码 `87001`（需要验证码）时，程序会自动：

1. 获取图形验证码图片
2. 使用 ddddocr 自动识别验证码
3. 将识别的验证码添加到登录请求中
4. 重新尝试登录

## 新增功能

### 1. 自动图形验证码识别
- 使用 `ddddocr` 库进行验证码识别
- 自动验证识别结果的长度（必须为5位）
- 支持多次重试（默认最多10次）

### 2. 智能重试机制
- 当验证码识别失败时自动重试
- 当验证码错误时自动获取新的验证码
- 每次重试间隔1秒，避免请求过于频繁

### 3. 详细的调试信息
- 显示验证码识别过程
- 显示每次重试的详细信息
- 显示登录响应的完整内容

## 使用方法

### 安装依赖
```bash
pip install ddddocr
```

### 运行程序
程序会自动处理图形验证码，无需手动干预：

```python
from 小米账号密码登录获取cookie import get_xiaomi_cookie

username = "your_phone_number"
password = "your_password"

result = get_xiaomi_cookie(username, password)
if result:
    print("登录成功!")
    print(f"Cookie: {result['formatted_cookie']}")
else:
    print("登录失败!")
```

## 错误处理

### 常见错误代码
- `87001`: 需要图形验证码（程序会自动处理）
- `20003`: 账号无效或不受支持
- `70016`: 验证码发送过于频繁

### 调试信息
程序会显示详细的调试信息，包括：
- 验证码识别结果
- HTTP请求和响应详情
- 错误代码和描述
- 重试过程

## 配置选项

### 重试次数
可以修改 `max_captcha_retries` 变量来调整最大重试次数：
```python
max_captcha_retries = 10  # 默认10次
```

### 重试间隔
可以修改 `time.sleep(1)` 来调整重试间隔：
```python
time.sleep(1)  # 默认1秒
```

## 技术实现

### 核心函数
1. `get_captcha_code(session)`: 获取并识别图形验证码
2. 修改后的 `get_xiaomi_cookie()`: 集成图形验证码处理逻辑

### 处理流程
1. 正常登录尝试
2. 检查响应代码是否为 87001
3. 如果需要验证码，调用 `get_captcha_code()` 获取验证码
4. 将验证码添加到登录数据中重新登录
5. 重复步骤直到成功或达到最大重试次数

## 注意事项

1. **依赖库**: 确保已安装 `ddddocr` 库
2. **网络环境**: 确保网络连接稳定
3. **识别准确率**: ddddocr 的识别准确率较高，但不是100%
4. **频率限制**: 避免过于频繁的请求，可能被小米服务器限制

## 测试

可以使用 `测试图形验证码.py` 脚本进行功能测试：
```bash
python 测试图形验证码.py
```

## 更新日志

- 添加了 ddddocr 图形验证码识别功能
- 集成了自动重试机制
- 增强了错误处理和调试信息
- 保持了与原有功能的完全兼容性
