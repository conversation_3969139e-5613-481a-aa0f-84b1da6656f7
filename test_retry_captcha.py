#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重试识别验证码并测试
"""

import requests
import json
import time
import base64
import io
from PIL import Image
import ddddocr

def safe_print(message):
    """安全打印函数"""
    try:
        print(message)
    except UnicodeEncodeError:
        print(message.encode('utf-8', errors='ignore').decode('utf-8'))

def parse_login_response(response):
    """解析登录响应"""
    try:
        text = response.text
        if '&&&START&&&' in text:
            json_str = text.split('&&&START&&&')[1]
            return json.loads(json_str)
        else:
            return json.loads(text)
    except:
        return None

def recognize_captcha(image_data):
    """识别验证码"""
    try:
        ocr = ddddocr.DdddOcr(show_ad=False)
        result = ocr.classification(image_data)
        return result
    except Exception as e:
        safe_print(f"OCR识别失败: {str(e)}")
        return None

def get_captcha_and_recognize_with_retry(max_retries=5):
    """获取并识别验证码，支持重试"""
    session = requests.Session()
    session.verify = False
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://account.xiaomi.com/'
    }
    session.headers.update(headers)
    
    for retry in range(max_retries):
        try:
            safe_print(f"=== 第 {retry + 1} 次尝试获取验证码 ===")
            
            # 获取验证码图片
            captcha_url = "https://account.xiaomi.com/pass/getCode?icodeType=login"
            captcha_response = session.get(captcha_url)
            
            if captcha_response.status_code == 200:
                # 获取ick cookie
                ick_cookie = None
                for cookie in session.cookies:
                    if cookie.name == 'ick':
                        ick_cookie = cookie.value
                        break
                
                if ick_cookie:
                    safe_print(f"✅ 成功获取ick cookie: {ick_cookie[:50]}...")
                    
                    # 识别验证码
                    image_data = captcha_response.content
                    captcha_code = recognize_captcha(image_data)
                    safe_print(f"OCR识别结果: '{captcha_code}' (长度: {len(captcha_code) if captcha_code else 0})")
                    
                    if captcha_code and len(captcha_code) == 5:
                        safe_print(f"✅ 验证码识别成功: {captcha_code}")
                        
                        # 立即测试验证码是否正确
                        if test_captcha(session, captcha_code, "18932145040"):
                            return session, captcha_code, ick_cookie
                        else:
                            safe_print(f"❌ 验证码 {captcha_code} 不正确，重试...")
                            time.sleep(1)
                            continue
                    else:
                        safe_print(f"❌ 验证码识别失败或长度不正确，重试...")
                        time.sleep(1)
                        continue
                else:
                    safe_print("❌ 未能获取ick cookie，重试...")
                    time.sleep(1)
                    continue
            else:
                safe_print(f"❌ 获取验证码图片失败，状态码: {captcha_response.status_code}，重试...")
                time.sleep(1)
                continue
                
        except Exception as e:
            safe_print(f"❌ 第 {retry + 1} 次尝试异常: {str(e)}")
            time.sleep(1)
            continue
    
    safe_print("❌ 所有重试都失败了")
    return None, None, None

def test_captcha(session, captcha_code, phone_number):
    """测试验证码是否正确"""
    try:
        sms_url = "https://account.xiaomi.com/pass/sendServiceLoginTicket"
        sms_data = {
            '_json': 'true',
            'user': phone_number,
            'captCode': captcha_code,
            'sid': 'passportapi',
            '_locale': 'zh_CN'
        }
        
        sms_response = session.post(sms_url, data=sms_data)
        sms_result = parse_login_response(sms_response)
        
        if sms_result and sms_result.get('code') == 0:
            safe_print(f"✅ 验证码 {captcha_code} 正确！短信发送成功")
            return True
        elif sms_result and sms_result.get('code') == 87001:
            safe_print(f"❌ 验证码 {captcha_code} 错误")
            return False
        else:
            safe_print(f"⚠️ 验证码测试返回其他结果: {sms_result}")
            return False
            
    except Exception as e:
        safe_print(f"❌ 测试验证码异常: {str(e)}")
        return False

def complete_login_flow(session, phone_number):
    """完成登录流程"""
    try:
        safe_print("=== 等待短信验证码 ===")
        safe_print("请查看手机短信，然后输入6位验证码...")
        
        # 等待用户输入短信验证码
        sms_code = input("请输入收到的6位短信验证码: ")
        
        if len(sms_code) == 6:
            # 进行登录验证
            safe_print("=== 开始serviceLoginTicketAuth验证 ===")
            
            auth_url = "https://account.xiaomi.com/pass/serviceLoginTicketAuth"
            auth_data = {
                'bizDeviceType': '',
                'needTheme': 'false',
                'theme': '',
                'showActiveX': 'false',
                'serviceParam': '{"checkSafePhone":false,"checkSafeAddress":false,"lsrp_score":0.0}',
                'callback': 'https://account.xiaomi.com/sts?sign=ZvAtJIzsDsFe60LdaPa76nNNP58%3D&followup=https%3A%2F%2Faccount.xiaomi.com%2Fpass%2Fauth%2Fsecurity%2Fhome&sid=passport',
                'qs': '%3Fcallback%3Dhttps%253A%252F%252Faccount.xiaomi.com%252Fsts%253Fsign%253DZvAtJIzsDsFe60LdaPa76nNNP58%25253D%2526followup%253Dhttps%25253A%25252F%25252Faccount.xiaomi.com%25252Fpass%25252Fauth%25252Fsecurity%25252Fhome%2526sid%253Dpassport%26sid%3Dpassport%26_group%3DDEFAULT',
                'sid': 'passport',
                '_sign': '2&V1_passport&q1Cy9JQ65bbDksgF65wKE1Lz/LI=',
                'user': phone_number,
                'ticket': sms_code,
                '_json': 'true',
                '_locale': 'zh_CN'
            }
            
            safe_print(f"=== serviceLoginTicketAuth请求 ===")
            safe_print(f"请求URL: {auth_url}")
            safe_print(f"请求数据: {auth_data}")
            
            auth_response = session.post(auth_url, data=auth_data)
            safe_print(f"serviceLoginTicketAuth响应状态码: {auth_response.status_code}")
            safe_print(f"serviceLoginTicketAuth响应内容: {auth_response.text}")
            
            auth_result = parse_login_response(auth_response)
            safe_print(f"serviceLoginTicketAuth解析结果: {auth_result}")
            
            if auth_result and auth_result.get('code') == 0:
                safe_print("✅ serviceLoginTicketAuth成功")
                
                # 获取location并访问
                location = auth_result.get('location')
                if location:
                    safe_print(f"=== 访问location获取最终cookies ===")
                    safe_print(f"Location: {location}")
                    
                    location_response = session.get(location)
                    safe_print(f"Location响应状态码: {location_response.status_code}")
                    
                    # 显示最终cookies
                    safe_print("=== 最终获取的cookies ===")
                    for cookie in session.cookies:
                        safe_print(f"{cookie.name}: {cookie.value}")
                
                return True
            else:
                safe_print(f"❌ serviceLoginTicketAuth失败: {auth_result}")
                return False
        else:
            safe_print("❌ 短信验证码长度不正确")
            return False
            
    except Exception as e:
        safe_print(f"❌ 完成登录流程异常: {str(e)}")
        return False

def main():
    """主函数"""
    phone_number = "18932145040"
    safe_print("=== 重试识别验证码测试 ===")
    safe_print(f"测试手机号: {phone_number}")
    
    # 获取并识别验证码（支持重试）
    session, captcha_code, ick_cookie = get_captcha_and_recognize_with_retry(max_retries=10)
    
    if session and captcha_code:
        safe_print(f"🎉 验证码识别成功: {captcha_code}")
        safe_print("短信已发送，请查看手机...")
        
        # 完成登录流程
        result = complete_login_flow(session, phone_number)
        
        if result:
            safe_print("🎉 完整测试成功完成！")
        else:
            safe_print("❌ 登录流程失败")
    else:
        safe_print("❌ 验证码识别失败")

if __name__ == "__main__":
    main()
