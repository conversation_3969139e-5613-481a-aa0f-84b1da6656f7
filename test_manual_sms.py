#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试手动输入验证码的流程
"""

import requests
import json
import time
import re
from urllib.parse import unquote

def safe_print(message):
    """安全打印函数"""
    try:
        print(message)
    except UnicodeEncodeError:
        print(message.encode('utf-8', errors='ignore').decode('utf-8'))

def parse_login_response(response):
    """解析登录响应"""
    try:
        text = response.text
        if '&&&START&&&' in text:
            json_str = text.split('&&&START&&&')[1]
            return json.loads(json_str)
        else:
            return json.loads(text)
    except:
        return None

def get_captcha_and_send_sms(phone_number):
    """获取验证码并发送短信"""
    session = requests.Session()
    session.verify = False
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://account.xiaomi.com/',
        'X-Requested-With': 'XMLHttpRequest'
    }
    session.headers.update(headers)
    
    safe_print(f"=== 开始测试手机号: {phone_number} ===")
    
    # 1. 先检查是否是新老号
    safe_print("=== 检查新老号状态 ===")
    try:
        # 获取验证码图片
        captcha_url = "https://account.xiaomi.com/pass/getCode?icodeType=resetpwd"
        captcha_response = session.get(captcha_url)
        
        if captcha_response.status_code == 200:
            # 获取ick cookie
            ick_cookie = None
            for cookie in session.cookies:
                if cookie.name == 'ick':
                    ick_cookie = cookie.value
                    break
            
            if ick_cookie:
                safe_print(f"✅ 成功获取ick cookie: {ick_cookie[:50]}...")
                
                # 这里需要OCR识别验证码，我们先跳过这步
                safe_print("⚠️ 跳过验证码识别，直接测试发送短信...")
            else:
                safe_print("❌ 未能获取ick cookie")
                return False
    except Exception as e:
        safe_print(f"❌ 获取验证码图片失败: {str(e)}")
        return False
    
    # 2. 直接尝试发送短信（使用登录类型的验证码）
    safe_print("=== 尝试发送短信验证码 ===")
    try:
        # 先获取登录类型的验证码图片
        login_captcha_url = "https://account.xiaomi.com/pass/getCode?icodeType=login"
        login_captcha_response = session.get(login_captcha_url)
        
        if login_captcha_response.status_code == 200:
            # 获取新的ick cookie
            ick_cookie = None
            for cookie in session.cookies:
                if cookie.name == 'ick':
                    ick_cookie = cookie.value
                    break
            
            if ick_cookie:
                safe_print(f"✅ 成功获取登录验证码ick cookie: {ick_cookie[:50]}...")
                
                # 这里我们需要手动输入验证码
                captcha_code = input("请查看验证码图片并输入5位验证码: ")
                
                if len(captcha_code) == 5:
                    # 发送短信
                    sms_url = "https://account.xiaomi.com/pass/sendServiceLoginTicket"
                    sms_data = {
                        '_json': 'true',
                        'user': phone_number,
                        'captCode': captcha_code,
                        'sid': 'passportapi',
                        '_locale': 'zh_CN'
                    }
                    
                    safe_print(f"发送短信请求...")
                    safe_print(f"请求URL: {sms_url}")
                    safe_print(f"请求数据: {sms_data}")
                    
                    sms_response = session.post(sms_url, data=sms_data)
                    safe_print(f"响应状态码: {sms_response.status_code}")
                    safe_print(f"响应内容: {sms_response.text}")
                    
                    # 解析响应
                    sms_result = parse_login_response(sms_response)
                    safe_print(f"解析结果: {sms_result}")
                    
                    if sms_result and sms_result.get('code') == 0:
                        safe_print("✅ 短信发送成功！")
                        
                        # 等待用户输入短信验证码
                        sms_code = input("请输入收到的6位短信验证码: ")
                        
                        if len(sms_code) == 6:
                            # 进行登录验证
                            safe_print("=== 开始登录验证 ===")
                            
                            # 这里应该调用serviceLoginTicketAuth
                            auth_url = "https://account.xiaomi.com/pass/serviceLoginTicketAuth"
                            auth_data = {
                                '_json': 'true',
                                'user': phone_number,
                                'vcode': sms_code,
                                'sid': 'passportapi',
                                '_locale': 'zh_CN'
                            }
                            
                            safe_print(f"=== serviceLoginTicketAuth请求 ===")
                            safe_print(f"请求URL: {auth_url}")
                            safe_print(f"请求数据: {auth_data}")
                            
                            auth_response = session.post(auth_url, data=auth_data)
                            safe_print(f"serviceLoginTicketAuth响应状态码: {auth_response.status_code}")
                            safe_print(f"serviceLoginTicketAuth响应内容: {auth_response.text}")
                            
                            auth_result = parse_login_response(auth_response)
                            safe_print(f"serviceLoginTicketAuth解析结果: {auth_result}")
                            
                            if auth_result and auth_result.get('code') == 0:
                                safe_print("✅ serviceLoginTicketAuth成功")
                                
                                # 获取location进行后续处理
                                location = auth_result.get('location')
                                if location:
                                    safe_print(f"获取到location: {location}")
                                    
                                    # 访问location获取最终cookies
                                    safe_print("=== 访问location获取cookies ===")
                                    location_response = session.get(location)
                                    safe_print(f"location响应状态码: {location_response.status_code}")
                                    
                                    # 显示最终cookies
                                    safe_print("=== 最终获取的cookies ===")
                                    for cookie in session.cookies:
                                        safe_print(f"{cookie.name}: {cookie.value}")
                                    
                                    return True
                                else:
                                    safe_print("❌ 未获取到location")
                                    return False
                            else:
                                safe_print(f"❌ serviceLoginTicketAuth失败: {auth_result}")
                                return False
                        else:
                            safe_print("❌ 短信验证码长度不正确")
                            return False
                    else:
                        safe_print(f"❌ 短信发送失败: {sms_result}")
                        return False
                else:
                    safe_print("❌ 验证码长度不正确")
                    return False
            else:
                safe_print("❌ 未能获取登录验证码ick cookie")
                return False
        else:
            safe_print(f"❌ 获取登录验证码图片失败，状态码: {login_captcha_response.status_code}")
            return False
            
    except Exception as e:
        safe_print(f"❌ 发送短信过程中发生异常: {str(e)}")
        return False

def main():
    """主函数"""
    phone_number = "18932145040"
    safe_print("=== 小米登录测试 - 手动输入验证码 ===")
    safe_print(f"测试手机号: {phone_number}")
    safe_print("注意: 需要手动输入图形验证码和短信验证码")
    
    result = get_captcha_and_send_sms(phone_number)
    
    if result:
        safe_print("🎉 测试成功完成！")
    else:
        safe_print("❌ 测试失败")

if __name__ == "__main__":
    main()
