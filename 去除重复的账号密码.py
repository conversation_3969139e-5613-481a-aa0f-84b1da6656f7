import os

def process_phone_passwords(input_file, output_file):
    # 用字典存储手机号和密码，后面的会覆盖前面的
    phone_password_dict = {}
    
    # 读取输入文件
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and '----' in line:
                phone, password = line.split('----')
                phone_password_dict[phone] = password
    
    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        for phone, password in phone_password_dict.items():
            f.write(f"{phone}----{password}\n")

if __name__ == "__main__":
    # 获取当前运行目录
    current_dir = os.getcwd()
    
    # 构建输入输出文件的完整路径
    input_file = os.path.join(current_dir, "白号账号密码全部.txt")
    output_file = os.path.join(current_dir, "白号账号密码去重.txt")
    
    process_phone_passwords(input_file, output_file)
    print("处理完成！结果已保存到", output_file) 