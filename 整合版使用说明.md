# 小米跑号整合版使用说明

## 🎯 功能特点

### 双平台支持
- **豪猪云 (haozhu)**: 稳定性好，API响应快速，支持高并发
- **椰子云 (yezicloud)**: 价格便宜，支持备用服务器，无需token认证

### 统一接口
- 相同的API调用方式
- 统一的配置管理
- 一致的错误处理
- 无缝平台切换

### 增强功能
- OCR重试机制：确保识别出5位验证码
- 验证码错误重试：遇到87001错误自动重试
- 手机号验证功能：支持忘记密码验证
- 自动注册支持：检测到不存在的号码自动注册

## 🔧 使用方法

### 1. 平台切换

修改文件顶部的 `CURRENT_PLATFORM` 变量：

```python
# 使用椰子云
CURRENT_PLATFORM = 'yezicloud'

# 使用豪猪云
CURRENT_PLATFORM = 'haozhu'
```

### 2. 配置参数

#### 豪猪云配置
```python
HAOZHU_CONFIG = {
    'username': 'your_username',      # 豪猪API用户名
    'password': 'your_password',      # 豪猪API密码
    'base_url': 'https://api.haozhuyun.com',
    'project_id': '20663',            # 项目ID
    'max_workers': 1,                 # 最大线程数
    'max_accounts': 1,                # 最大账号数量限制
    'use_forget_password_check': True,    # 是否验证手机号
    'auto_register_if_not_exist': True,   # 是否自动注册
    'max_ocr_retries': 5,             # OCR重试次数
    'max_captcha_retries': 20         # 验证码重试次数
}
```

#### 椰子云配置
```python
YEZICLOUD_CONFIG = {
    'username': 'your_username',      # 椰子云账号
    'password': 'your_password',      # 椰子云密码
    'base_url': 'http://api.sqhyw.net:90',
    'backup_url': 'http://api.nnanx.com:90',  # 备用服务器
    'project_id': '10008',            # 项目ID
    'max_workers': 1,                 # 最大线程数
    'max_accounts': 1,                # 最大账号数量限制
    'use_forget_password_check': True,    # 是否验证手机号
    'auto_register_if_not_exist': True,   # 是否自动注册
    'max_ocr_retries': 5,             # OCR重试次数
    'max_captcha_retries': 20         # 验证码重试次数
}
```

### 3. 运行程序

```bash
python 小米跑号整合版.py
```

## ⚙️ 配置说明

### 基础配置
- `project_id`: 项目ID，不同平台使用不同的值
- `max_workers`: 最大线程数，建议1-3
- `max_accounts`: 最大账号数量限制

### 验证配置
- `use_forget_password_check`: 是否使用忘记密码验证手机号
  - `True`: 先验证手机号状态，再决定处理方式
  - `False`: 跳过验证，直接发送验证码

- `auto_register_if_not_exist`: 当手机号不存在时是否自动注册
  - `True`: 检测到不存在时继续发送验证码（小米会自动创建账号）
  - `False`: 检测到不存在时跳过该号码

### 重试配置
- `max_ocr_retries`: OCR识别重试次数（确保识别出5位验证码）
- `max_captcha_retries`: 验证码重试次数（包括87001错误重试）

## 📊 推荐配置

### 追求稳定性
```python
CURRENT_PLATFORM = 'haozhu'
HAOZHU_CONFIG = {
    # ... 其他配置
    'max_workers': 1,
    'max_accounts': 10,
    'use_forget_password_check': True,
    'auto_register_if_not_exist': True,
    'max_ocr_retries': 5,
    'max_captcha_retries': 20
}
```

### 追求性价比
```python
CURRENT_PLATFORM = 'yezicloud'
YEZICLOUD_CONFIG = {
    # ... 其他配置
    'max_workers': 1,
    'max_accounts': 5,
    'use_forget_password_check': True,
    'auto_register_if_not_exist': True,
    'max_ocr_retries': 5,
    'max_captcha_retries': 20
}
```

## 🔄 工作流程

1. **平台初始化**
   - 根据 `CURRENT_PLATFORM` 创建对应的API实例
   - 显示当前平台配置信息

2. **平台登录**
   - 豪猪云：使用用户名密码获取token
   - 椰子云：直接使用（无需单独登录）

3. **账号处理循环**
   - 获取手机号
   - 检查白名单/已有cookies
   - 验证手机号（可选）
   - 发送验证码（支持重试）
   - 获取短信验证码
   - 获取小米cookie
   - 保存结果

4. **错误处理**
   - 自动重试机制
   - 详细的错误日志
   - 计数器回退保护

## 🚨 注意事项

1. **平台切换**
   - 修改 `CURRENT_PLATFORM` 后需要重启程序
   - 确保对应平台的配置正确

2. **项目ID**
   - 不同平台使用不同的项目ID
   - 确保项目ID有效且有余额

3. **线程数量**
   - 建议从1开始测试
   - 根据平台稳定性调整

4. **重试次数**
   - OCR重试次数不宜过高（建议5次）
   - 验证码重试次数可以适当增加（建议20次）

## 🔍 故障排除

### 登录失败
- 检查用户名密码是否正确
- 检查网络连接
- 检查平台服务器状态

### 获取手机号失败
- 检查项目ID是否正确
- 检查账户余额
- 检查项目是否有可用号码

### 验证码识别失败
- 增加OCR重试次数
- 检查网络连接
- 检查小米服务器状态

### 短信接收失败
- 检查手机号是否有效
- 增加等待时间
- 检查短信平台状态

## 📝 更新日志

### v1.0.0
- 整合豪猪和椰子两个平台
- 统一API接口
- 支持配置化平台切换
- 增强的重试机制
- 详细的日志输出
