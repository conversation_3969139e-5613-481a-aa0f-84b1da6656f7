import requests
import os
import json
import uuid
import hashlib
import base64
from urllib.parse import quote
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
from urllib.parse import quote, quote_plus
import time

def md5_encrypt(text):
    """MD5加密"""
    return hashlib.md5(text.encode('utf-8')).hexdigest().upper()

def parse_login_response(response):
    """解析登录响应"""
    try:
        text = response.text.replace('&&&START&&&', '')
        return json.loads(text)
    except Exception as e:
        print(f"❌ 解析响应异常: {str(e)}")
    return None

def generate_signed_url(response_json):
    """根据API响应生成带签名的URL"""
    data = json.loads(response_json.replace('&&&START&&&', ''))
    nonce = data['nonce']
    ssecurity = data['ssecurity']
    location = data['location']
    
    sign_data = f"nonce={nonce}&{ssecurity}"
    sha1_hash = hashlib.sha1(sign_data.encode('utf-8')).digest()
    signature = base64.b64encode(sha1_hash).decode('utf-8')
    url_encoded_sign = quote(signature)
    
    if '?' in location:
        signed_url = f"{location}&_userIdNeedEncrypt=true&clientSign={url_encoded_sign}"
    else:
        signed_url = f"{location}?_userIdNeedEncrypt=true&clientSign={url_encoded_sign}"
    
    return signed_url

def handle_verification(session, notification_url):
    """处理验证流程"""
    try:
        # 第一步：访问authStart获取identity_session
        auth_start_response = session.get(
            notification_url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Linux; Android 9; MIX 2S Build/PKQ1.180729.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/72.0.3626.121 Mobile Safari/537.36 PassportSDK/PassportHybridView/accountsdk-18.11.26 XiaoMi/HybridView/',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'X-Requested-With': 'com.xiaomi.account'
            },
            allow_redirects=True
        )
        
        print("\n🔍 验证第一步响应:")
        print(f"状态码: {auth_start_response.status_code}")
        print(f"Headers: {dict(auth_start_response.headers)}")
        print(f"Cookies: {auth_start_response.cookies.get_dict()}")
        
        # 获取重定向后的URL中的context参数
        final_url = auth_start_response.url
        context = None
        if 'context=' in final_url:
            context = final_url.split('context=')[1].split('&')[0]
        
        if not context:
            print("❌ 获取context参数失败")
            return None
            
        # 第二步：获取验证问题列表
        list_url = f"https://account.xiaomi.com/identity/list?sid=passportapi&supportedMask=0&context={context}"
        list_response = session.get(
            list_url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Linux; Android 9; MIX 2S Build/PKQ1.180729.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/72.0.3626.121 Mobile Safari/537.36 PassportSDK/PassportHybridView/accountsdk-18.11.26 XiaoMi/HybridView/',
                'Accept': 'application/json, text/plain, */*',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': final_url,
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
            }
        )
        
        print("\n🔍 验证第二步响应:")
        print(f"状态码: {list_response.status_code}")
        print(f"Headers: {dict(list_response.headers)}")
        print(f"Cookies: {list_response.cookies.get_dict()}")
        print(f"响应内容: {list_response.text}")
        
        if list_response.status_code != 200:
            print("❌ 获取验证问题列表失败")
            return None
            
        # 获取identity_session
        identity_session = session.cookies.get('identity_session')
        if not identity_session:
            print("❌ 获取identity_session失败")
            return None
            
        # 第三步：提交密保答案
        answer_url = "https://account.xiaomi.com/identity/auth/answerQuestion"
        answer_data = {
            '_flag': '16',
            'questions': json.dumps([
                {"q": "您外公的姓名", "p": 1, "a": "外公********"},
                {"q": "您小学六年级班主任的名字", "p": 3, "a": "六年级班主任********"},
                {"q": "您外婆的姓名", "p": 0, "a": "外婆********"}
            ]),
            '_json': 'true'
        }
        
        answer_response = session.post(
            answer_url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Linux; Android 9; MIX 2S Build/PKQ1.180729.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/72.0.3626.121 Mobile Safari/537.36 PassportSDK/PassportHybridView/accountsdk-18.11.26 XiaoMi/HybridView/',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Accept': 'application/json, text/plain, */*',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': final_url,
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
            },
            data=answer_data
        )
        
        print("\n🔍 验证第三步响应:")
        print(f"状态码: {answer_response.status_code}")
        print(f"Headers: {dict(answer_response.headers)}")
        print(f"Cookies: {answer_response.cookies.get_dict()}")
        print(f"响应内容: {answer_response.text}")
        
        if answer_response.status_code != 200:
            print("❌ 提交密保答案失败")
            return None
            
        result = parse_login_response(answer_response)
        if result and result.get('code') == 0:
            return result.get('location')
            
        return None
    except Exception as e:
        print(f"❌ 验证流程异常: {str(e)}")
        return None

def get_xiaomi_cookie(username, password):
    """获取小米账号Cookie"""
    session = requests.Session()
    session.verify = False  # 完全禁用SSL验证
    device_id = str(uuid.uuid4()).replace('-', '')
    base_url = "https://account.xiaomi.com"
    
    login_url = f"{base_url}/pass/serviceLoginAuth2"
    login_data = {
        'qs': '%253F_json%253Dtrue%2526sid%253Dpassportapi%2526_locale%253Dzh_CN',
        'callback': 'https://api.account.xiaomi.com/sts?sid=passportapi',
        '_json': 'true',
        '_sign': 'l8bGcCEpX5MKvqcJxU5gDg5DsLE%3D',
        'user': username,
        'hash': md5_encrypt(password),
        'sid': 'passportapi',
        '_locale': 'zh_CN'
    }

    try:
        response = session.post(
            login_url,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM',
                'Accept-Encoding': 'gzip',
                'Connection': 'Keep-Alive'
            },
            data=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = parse_login_response(response)
            if result:
                if result.get('code') == 0 and result.get('notificationUrl'):
                    print("⚠️ 需要验证，开始验证流程")
                    location = handle_verification(session, result.get('notificationUrl'))
                    if location:
                        result['location'] = location
                        print("✅ 验证成功")
                    else:
                        print("❌ 验证失败")
                        return None
                elif result.get('code') == 0 and result.get('location'):
                    print("✅ 登录成功")
                else:
                    print(f"❌ 登录失败: {result.get('desc', '未知错误')}")
                    return None

            sts_url = result.get('location')
            if sts_url:
                sts_response = session.get(
                    sts_url,
                    headers={'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'},
                    verify=False,
                    timeout=10
                )
                
                if sts_response.status_code == 200:
                    service_tokens = [c.value for c in sts_response.cookies if c.name == 'serviceToken']
                    if service_tokens:
                        service_token = service_tokens[0]
                        session.cookies.set('serviceToken', service_token, domain='.xiaomi.com', path='/')
                        
                        # 添加音乐服务验证步骤
                        try:
                            # 第一个音乐验证请求
                            music_verify_url = "https://account.xiaomi.com/pass/serviceLogin"
                            music_params = {
                                '_json': 'true',
                                'appName': 'com.miui.player',
                                'sid': 'music',
                                '_locale': 'zh_CN'
                            }
                            
                            print(f"\n🎵 发送音乐服务验证请求: {music_verify_url}")
                            music_response = session.get(
                                music_verify_url,
                                params=music_params,
                                headers={
                                    'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
                                },
                                verify=False,
                                timeout=5
                            )
                            
                            if music_response.status_code == 200:
                                music_result = parse_login_response(music_response)
                                if music_result and music_result.get('code') == 0:
                                    print(f"🎵 音乐服务验证成功，开始生成签名URL")
                                    
                                    # 构造需要签名的JSON响应
                                    music_response_json = f"&&&START&&&{json.dumps(music_result)}"
                                    
                                    # 生成音乐服务签名URL
                                    music_signed_url = generate_signed_url(music_response_json)
                                    print(f"🎵 生成音乐服务签名URL: {music_signed_url}")
                                    
                                    # 请求音乐服务签名URL
                                    music_sts_response = session.get(
                                        music_signed_url,
                                        headers={
                                            'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
                                        },
                                        verify=False,
                                        timeout=5
                                    )
                                    
                                    # 从签名URL响应中提取关键cookie
                                    if music_sts_response.status_code == 200:
                                        music_sts_cookies = music_sts_response.cookies.get_dict()
                                        print("\n🔍 从签名URL响应中提取的Cookies:")
                                        print(music_sts_cookies)
                                        
                                        # 提取并保存关键cookie
                                        if 'serviceToken' in music_sts_cookies:
                                            session.cookies.set(
                                                'serviceToken', 
                                                music_sts_cookies['serviceToken'],
                                                domain='.xiaomi.com',
                                                path='/'
                                            )
                                            print(f"✅ 已更新serviceToken: {music_sts_cookies['serviceToken']}")
                                        
                                        if 'oauth2.0_slh' in music_sts_cookies:
                                            session.cookies.set(
                                                'oauth2.0_slh',
                                                music_sts_cookies['oauth2.0_slh'],
                                                domain='.xiaomi.com',
                                                path='/'
                                            )
                                        
                                        if 'oauth2.0_ph' in music_sts_cookies:
                                            session.cookies.set(
                                                'oauth2.0_ph',
                                                music_sts_cookies['oauth2.0_ph'],
                                                domain='.xiaomi.com',
                                                path='/'
                                            )
                                        
                                        if 'cUserId' in music_sts_cookies:
                                            session.cookies.set(
                                                'cUserId',
                                                music_sts_cookies['cUserId'],
                                                domain='.xiaomi.com',
                                                path='/'
                                            )
                                        
                                        if 'uLocale' in music_sts_cookies:
                                            session.cookies.set(
                                                'uLocale',
                                                music_sts_cookies['uLocale'],
                                                domain='.xiaomi.com',
                                                path='/'
                                            )
                                        
                                        print("\n🔍 更新后的会话Cookies:")
                                        print(session.cookies.get_dict())
                                    
                                    # 显示两个请求后的所有cookie
                                    print("\n🔍 音乐服务验证后的Cookies:")
                                    print(f"第一次请求Cookies: {music_response.cookies.get_dict()}")
                                    print(f"第二次请求Cookies: {music_sts_response.cookies.get_dict()}")
                                    print(f"当前会话Cookies: {session.cookies.get_dict()}")
                                    
                                    if music_sts_response.status_code == 200:
                                        print("✅ 音乐服务验证成功")
                                        
                                        # 新增：显示详细响应信息
                                        print("\n🔍 音乐服务验证响应详情:")
                                        print(f"状态码: {music_sts_response.status_code}")
                                        print(f"Headers: {dict(music_sts_response.headers)}")
                                        print(f"Cookies: {music_sts_response.cookies.get_dict()}")
                                        print(f"响应内容: {music_sts_response.text[:1000]}")  # 显示前1000个字符防止过长
                                        
                                        # 解析并显示关键token信息
                                        try:
                                            response_data = json.loads(music_sts_response.text)
                                            if 'access_token' in response_data:
                                                print("\n🎵 获取到的音乐服务Token:")
                                                print(f"access_token: {response_data['access_token']}")
                                                print(f"expires_in: {response_data['expires_in']}")
                                                print(f"refresh_token: {response_data.get('refresh_token', '无')}")
                                                print(f"openId: {response_data.get('openId', '无')}")
                                        except Exception as e:
                                            print(f"⚠️ 解析音乐服务响应异常: {str(e)}")

                        except Exception as e:
                            print(f"⚠️ 音乐服务验证请求异常: {str(e)}")
                        
                        # 添加第二个音乐验证请求（oauth2.0验证）
                        try:
                            music2_params = {
                                '_json': 'true',
                                'appName': 'com.miui.player',
                                'sid': 'oauth2.0',
                                '_locale': 'zh_CN'
                            }
                            
                            print(f"\n🎵 发送音乐服务oauth2.0验证请求")
                            music2_response = session.get(
                                "https://account.xiaomi.com/pass/serviceLogin",
                                params=music2_params,
                                headers={
                                    'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
                                },
                                verify=False,
                                timeout=5
                            )
                            
                            # 添加响应日志输出
                            print(f"\n🔍 oauth2.0验证响应详情:")
                            print(f"状态码: {music2_response.status_code}")
                            print(f"Headers: {dict(music2_response.headers)}")
                            print(f"Cookies: {music2_response.cookies.get_dict()}")
                            print(f"响应内容: {music2_response.text[:500]}")
                            
                            if music2_response.status_code == 200:
                                music2_result = parse_login_response(music2_response)
                                if music2_result and music2_result.get('code') == 0:
                                    print("✅ oauth2.0验证成功")
                                    
                                    # 新增：获取scope信息
                                    try:
                                        scope_url = "https://account.xiaomi.com/oauth2/user-credentials/scopes"
                                        scope_params = {
                                            'scope': '1 3',
                                            'pt': '0',
                                            'client_id': '2882303761517248199',
                                            'sid': 'oauth2.0'
                                        }
                                        
                                        print("\n🔍 发送scope信息请求")
                                        scope_response = session.get(
                                            scope_url,
                                            params=scope_params,
                                            headers={
                                                'User-Agent': 'okhttp/4.10.0',
                                                'Accept-Encoding': 'gzip',
                                                'Connection': 'Keep-Alive'
                                            },
                                            verify=False,
                                            timeout=10
                                        )
                                        
                                        if scope_response.status_code == 200:
                                            scope_data = scope_response.json()
                                            if scope_data.get('status') == 'ok':
                                                code = scope_data.get('code')
                                                print(f"✅ 成功获取scope信息，code: {code}")
                                                # 这里可以保存code和client_id供后续使用
                                    except Exception as e:
                                        print(f"⚠️ 获取scope信息异常: {str(e)}")
                                    
                                    # 新增：构造需要签名的JSON响应
                                    music2_response_json = f"&&&START&&&{json.dumps(music2_result)}"
                                    
                                    # 新增：生成oauth2.0签名URL
                                    music2_signed_url = generate_signed_url(music2_response_json)
                                    print(f"🎵 生成oauth2.0签名URL: {music2_signed_url}")
                                    
                                    # 新增：请求oauth2.0签名URL
                                    music2_sts_response = session.get(
                                        music2_signed_url,
                                        headers={
                                            'User-Agent': 'Mi 10/umi; MIUI/V14.0.4.0.TJBCNXM'
                                        },
                                        verify=False,
                                        timeout=5
                                    )
                                    
                                    # 新增：显示签名URL请求后的Cookies
                                    print("\n🔍 oauth2.0签名URL请求后的Cookies:")
                                    print(f"签名URL请求Cookies: {music2_sts_response.cookies.get_dict()}")
                                    print(f"当前会话Cookies: {session.cookies.get_dict()}")
                                    
                                    if music2_sts_response.status_code == 200:
                                        print("✅ oauth2.0签名URL请求成功")
                                        
                                        # 新增：获取音乐token
                                        try:
                                            print("\n🎵 开始获取音乐服务token...")
                                            # 从响应中提取两个serviceToken
                                            music_service_token = session.cookies.get('serviceToken', domain='.xiaomi.com')
                                            oauth2_service_token = music2_sts_response.cookies.get('serviceToken')
                                            
                                            # 确保有userId
                                            user_id = result.get('userId')
                                            if not user_id:
                                                # 尝试从cookies中获取
                                                # 获取所有userId的cookie
                                                user_ids = [cookie.value for cookie in session.cookies if cookie.name == 'userId']
                                                if user_ids:
                                                    # 使用最后一个userId（通常是最新的）
                                                    user_id = user_ids[-1]
                                                    print(f"✅ 从cookies中获取到userId: {user_id}")
                                                else:
                                                    # 尝试从response中获取
                                                    if music2_sts_response and music2_sts_response.cookies:
                                                        user_ids = [cookie.value for cookie in music2_sts_response.cookies if cookie.name == 'userId']
                                                        if user_ids:
                                                            user_id = user_ids[-1]
                                                            print(f"✅ 从response中获取到userId: {user_id}")
                                            
                                            print(f"\n🔍 当前userId: {user_id}")
                                            if not user_id:
                                                print("❌ 无法获取userId")
                                                return {
                                                    'cookies': session.cookies.get_dict(),
                                                    'tokens': {
                                                        'passToken': result.get('passToken'),
                                                        'cUserId': result.get('cUserId'),
                                                        'serviceToken': service_token
                                                    }
                                                }
                                            
                                            # 修改调用方式，传入music2_sts_response参数
                                            print(f"\n🔍 准备调用get_music_token:")
                                            print(f"userId: {user_id}")
                                            print(f"music_service_token: {music_service_token}")
                                            print(f"oauth2_service_token: {oauth2_service_token}")
                                            
                                            music_token = get_music_token(
                                                session=session,
                                                user_id=user_id,
                                                music_service_token=music_service_token,
                                                oauth2_service_token=oauth2_service_token,
                                                music2_sts_response=music2_sts_response
                                            )
                                            
                                            if music_token:
                                                print("✅ 成功获取音乐服务token")
                                                return {
                                                    'cookies': session.cookies.get_dict(),
                                                    'tokens': {
                                                        'passToken': result.get('passToken'),
                                                        'cUserId': result.get('cUserId'),
                                                        'serviceToken': service_token,
                                                        'musicToken': music_token
                                                    }
                                                }
                                        except Exception as e:
                                            print(f"❌ 获取音乐token失败: {str(e)}")

                        except Exception as e:
                            print(f"⚠️ 第二个音乐服务验证请求异常: {str(e)}")
                        
                        return {
                            'cookies': session.cookies.get_dict(),
                            'tokens': {
                                'passToken': result.get('passToken'),
                                'cUserId': result.get('cUserId'),
                                'serviceToken': service_token
                            }
                        }
    except Exception as e:
        print(f"❌ 登录请求异常: {str(e)}")
        return None

def read_accounts(file_path):
    """读取账号密码文件"""
    accounts = []
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ 账号文件 {file_path} 不存在，请创建")
            print("文件格式应为：手机号----密码，每行一个账号")
            return accounts
            
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and '----' in line:
                    phone, pwd = line.split('----', 1)
                    accounts.append({'phone': phone, 'password': pwd})
                else:
                    print(f"⚠️ 忽略格式不正确的行: {line}")
    except Exception as e:
        print(f"❌ 读取账号文件失败: {str(e)}")
    return accounts

def get_music_token(session, user_id, music_service_token, oauth2_service_token, music2_sts_response=None):
    """获取音乐服务access token"""
    try:
        # 如果user_id为None，尝试从cookies中获取
        if not user_id:
            user_id = session.cookies.get('userId')
            if user_id:
                print(f"✅ 从cookies中获取到userId: {user_id}")
            else:
                print("❌ 无法获取userId")
                return None

        # 第一步：获取scope信息
        scope_url = "https://account.xiaomi.com/oauth2/user-credentials/scopes"
        scope_params = {
            'scope': '1 3',
            'pt': '0',
            'client_id': '2882303761517248199',
            'sid': 'oauth2.0'
        }
        
        print("\n🔍 发送scope请求...")
        scope_response = session.get(
            scope_url,
            params=scope_params,
            headers={
                'User-Agent': 'okhttp/4.10.0',
                'Accept-Encoding': 'gzip',
                'Connection': 'Keep-Alive'
            },
            verify=False,
            timeout=10
        )
        
        if scope_response.status_code != 200:
            print(f"❌ Scope请求失败，状态码: {scope_response.status_code}")
            return None
            
        scope_data = scope_response.json()
        if scope_data.get('status') != 'ok':
            print(f"❌ Scope响应异常: {scope_data}")
            return None
            
        code = scope_data.get('code')
        print(f"✅ 获取到原始code: {code}")
        
        # 立即对code进行编码处理
        encoded_code = quote_plus(code)
        print(f"✅ 编码后code: {encoded_code}")
        
        # 确保获取到有效的token
        if not oauth2_service_token:
            print("❌ 无法获取有效的oauth2_serviceToken")
            return None
            
        if not music_service_token:
            print("❌ 无法获取有效的music_service_token")
            return None
            
        # 构造完整Cookie
        combined_cookies = {
            'oauth2.0_serviceToken': oauth2_service_token,
            'sts': music_service_token
        }
        
        # 第二步：获取access token
        # 构建带查询参数的URL
        token_url = "https://api.music.xiaomi.com/music/v1/id/qqmusic/access_token"
        token_params = {
            'grant_type': 'password',
            'pt': '0',
            'token_type': 'bearer',
            'client_id': '2882303761517248199',
            'sid': 'oauth2.0',
            'code': encoded_code,
            'user_id': user_id
        }
        
        # 构建查询字符串
        query_string = '&'.join([f"{k}={v}" for k, v in token_params.items()])
        full_url = f"{token_url}?{query_string}"
        
        print("\n🔍 发送音乐token请求:")
        print(f"请求URL: {full_url}")
        
        # 发送GET请求
        token_response = session.post(
            full_url,
            headers={
                'User-Agent': 'okhttp/4.10.0',
                'Cookie': f"oauth2.0_serviceToken={oauth2_service_token}; sts={music_service_token}",
                'Accept-Encoding': 'gzip',
                'Connection': 'Keep-Alive',
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Host': 'api.music.xiaomi.com'
            },
            verify=False,
            timeout=10
        )

        # 新增响应日志输出
        print("\n🔍 音乐token响应详情:")
        print(f"状态码: {token_response.status_code}")
        print(f"Headers: {dict(token_response.headers)}")
        print(f"Cookies: {token_response.cookies.get_dict()}")
        print(f"响应内容长度: {len(token_response.text)}")  # 添加这行来显示内容长度
        print(f"响应内容: {token_response.text}")  # 显示完整响应内容
        print(f"响应编码: {token_response.encoding}")
        print(f"响应URL: {token_response.url}")
        print(f"响应请求方法: {token_response.request.method}")
        print(f"请求头: {dict(token_response.request.headers)}")

        if len(token_response.text) == 0:
            print("⚠️ 警告：响应内容为空")
            return None

        if token_response.status_code != 200:
            print(f"❌ 音乐服务验证失败，状态码: {token_response.status_code}")
            return None

        # 尝试解析JSON，如果失败也显示原始内容
        try:
            token_data = token_response.json()
            print("\n🎵 解析后的token数据:")
            print(f"access_token: {token_data.get('access_token')}")
            print(f"expires_in: {token_data.get('expires_in')}")
            print(f"refresh_token: {token_data.get('refresh_token', '无')}")
            print(f"openId: {token_data.get('openId', '无')}")
            return token_data
        except Exception as e:
            print(f"⚠️ 解析token响应异常: {str(e)}")
            print(f"原始响应内容: {token_response.text}")  # 显示原始响应内容
            return None

    except Exception as e:
        print(f"❌ 音乐服务验证异常: {str(e)}")
        return None

if __name__ == '__main__':
    # 定义账号密码文件路径
    account_file = "小米账号密码.txt"  # 修改为正确的文件名
    
    # 从文件读取小米账号密码
    accounts = read_accounts(account_file)
    
    if not accounts:
        print("❌ 没有找到有效账号，请检查文件格式：手机号----密码")
        exit()
    
    for account in accounts:
        print(f"\n正在处理账号: {account['phone']}")
        result = get_xiaomi_cookie(account['phone'], account['password'])
        if result:
            print("\n获取到的Cookie信息:")
            print(json.dumps(result, indent=2, ensure_ascii=False))